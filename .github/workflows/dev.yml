on: 
  push:
    branches:
      - dev
name: 🚀 Deploy website to UAT on push
jobs:
  web-deploy:
    name: 🎉 Deploy to UAT
    runs-on: ubuntu-latest
    steps:
    - name: 🚚 Get latest code
      uses: actions/checkout@v4

    - name: 📦 Install PHP
      uses: "shivammathur/setup-php@v2"
      with:
        php-version: "8.4"

    - name: 📦 Install Composer
      uses: "ramsey/composer-install@v3"

    - name: 📦 Install Composer dependencies
      run: composer install

    - name: Use Node.js 22
      uses: actions/setup-node@v4
      with:
        node-version: '22'

    - name: 📦 Install Node.js dependencies
      run: npm install

    - name: 🔨 Build Project
      run: npm run build

    - name: 📂 Sync files
      uses: SamKirkland/FTP-Deploy-Action@v4.3.5
      with:
        server: ftp.varvik.hemsida.eu
        username: <EMAIL>
        password: ${{ secrets.uat_ftp_password }}
        server-dir: /vhosts/varvik-new.vmate.se/wp-content/themes/varvik-theme/
        exclude: |
          node_modules/
          .git/
          .github/
          .env
          .env.example
          composer.json
          composer.lock
          package.json
          package-lock.json
          readme.md
          .gitignore