{"name": "npm-run-all", "version": "4.1.5", "description": "A CLI tool to run multiple npm-scripts in parallel or sequential.", "bin": {"run-p": "bin/run-p/index.js", "run-s": "bin/run-s/index.js", "npm-run-all": "bin/npm-run-all/index.js"}, "main": "lib/index.js", "files": ["bin", "lib", "docs"], "engines": {"node": ">= 4"}, "scripts": {"_mocha": "mocha \"test/*.js\" --timeout 120000", "clean": "rimraf .nyc_output coverage jsdoc \"test-workspace/{build,test.txt}\"", "docs": "jsdoc -c jsdoc.json", "lint": "eslint bin lib scripts test \"test-workspace/tasks/*.js\"", "pretest": "node scripts/make-slink.js && npm run lint", "preversion": "npm test", "postversion": "git push && git push --tags", "test": "nyc --require babel-register npm run _mocha", "watch": "npm run _mocha -- --require babel-register --watch --growl", "codecov": "nyc report -r lcovonly && codecov"}, "dependencies": {"ansi-styles": "^3.2.1", "chalk": "^2.4.1", "cross-spawn": "^6.0.5", "memorystream": "^0.3.1", "minimatch": "^3.0.4", "pidtree": "^0.3.0", "read-pkg": "^3.0.0", "shell-quote": "^1.6.1", "string.prototype.padend": "^3.0.0"}, "devDependencies": {"@types/node": "^4.9.1", "babel-plugin-transform-async-to-generator": "^6.24.1", "babel-preset-power-assert": "^2.0.0", "babel-register": "^6.26.0", "codecov": "^3.1.0", "eslint": "^4.19.1", "eslint-config-mysticatea": "^12.0.0", "fs-extra": "^7.0.1", "mocha": "^5.2.0", "nyc": "^11.9.0", "p-queue": "^2.4.2", "power-assert": "^1.6.1", "rimraf": "^2.6.2", "yarn": "^1.12.3"}, "repository": "mysticatea/npm-run-all", "keywords": ["cli", "command", "commandline", "tool", "npm", "npm-scripts", "run", "sequential", "serial", "parallel", "task"], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/npm-run-all/issues"}, "homepage": "https://github.com/mysticatea/npm-run-all"}