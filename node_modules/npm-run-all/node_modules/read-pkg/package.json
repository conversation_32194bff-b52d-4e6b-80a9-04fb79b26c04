{"name": "read-pkg", "version": "3.0.0", "description": "Read a package.json file", "license": "MIT", "repository": "sindresorhus/read-pkg", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["json", "read", "parse", "file", "fs", "graceful", "load", "pkg", "package", "normalize"], "dependencies": {"load-json-file": "^4.0.0", "normalize-package-data": "^2.3.2", "path-type": "^3.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}}