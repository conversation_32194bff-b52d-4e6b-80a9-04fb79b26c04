'use strict';

var ToLength = require('es-abstract/2024/ToLength');
var ToString = require('es-abstract/2024/ToString');
var RequireObjectCoercible = require('es-object-atoms/RequireObjectCoercible');
var callBound = require('call-bind/callBound');

var $slice = callBound('String.prototype.slice');

module.exports = function padEnd(maxLength) {
	var O = RequireObjectCoercible(this);
	var S = ToString(O);
	var stringLength = ToLength(S.length);
	var fillString;
	if (arguments.length > 1) {
		fillString = arguments[1];
	}
	var filler = typeof fillString === 'undefined' ? '' : ToString(fillString);
	if (filler === '') {
		filler = ' ';
	}
	var intMaxLength = ToLength(maxLength);
	if (intMaxLength <= stringLength) {
		return S;
	}
	var fillLen = intMaxLength - stringLength;
	while (filler.length < fillLen) {
		var fLen = filler.length;
		var remainingCodeUnits = fillLen - fLen;
		filler += fLen > remainingCodeUnits ? $slice(filler, 0, remainingCodeUnits) : filler;
	}

	var truncatedStringFiller = filler.length > fillLen ? $slice(filler, 0, fillLen) : filler;
	return S + truncatedStringFiller;
};
