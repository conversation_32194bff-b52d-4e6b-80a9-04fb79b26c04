{"core/audits/accessibility/accesskeys.js | description": {"message": "<PERSON><PERSON><PERSON> khoá truy cập cho phép người dùng chuyển nhanh đến một phần của trang. Đ<PERSON> điều hướng đúng c<PERSON>ch, mỗi khoá truy cập phải là duy nhất. [Tìm hiểu thêm về khoá truy cập](https://dequeuniversity.com/rules/axe/4.10/accesskeys)."}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> giá trị của `[accesskey]` không phải là duy nhất"}, "core/audits/accessibility/accesskeys.js | title": {"message": "<PERSON><PERSON><PERSON> giá trị của `[accesskey]` là duy nhất"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "Mỗi `role` của Ứng dụng Internet phong phú dễ dùng (ARIA) hỗ trợ một tập hợp con cụ thể các thuộc tính `aria-*`. Nếu không trùng khớp, các thuộc tính `aria-*` sẽ bị vô hiệu. [Tìm hiểu cách khớp các thuộc tính của Ứng dụng Internet phong phú dễ dùng (ARIA) với vai trò của những thuộc tính này](https://dequeuniversity.com/rules/axe/4.10/aria-allowed-attr)."}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> thu<PERSON> `[aria-*]` không khớp với vai trò tương <PERSON>ng"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "<PERSON><PERSON><PERSON> thu<PERSON> `[aria-*]` khớp với vai trò tương <PERSON>ng"}, "core/audits/accessibility/aria-allowed-role.js | description": {"message": "<PERSON><PERSON><PERSON>u phần tử HTML chỉ có thể được chỉ định những vai trò nhất định của ARIA. Việc sử dụng vai trò của ARIA cho các phần tử không được phép có thể ảnh hưởng đến khả năng hỗ trợ tiếp cận của trang web. [Tìm hiểu thêm về các vai trò của ARIA](https://dequeuniversity.com/rules/axe/4.10/aria-allowed-role)."}, "core/audits/accessibility/aria-allowed-role.js | failureTitle": {"message": "Sử dụng vai trò của ARIA cho các phần tử không tương thích"}, "core/audits/accessibility/aria-allowed-role.js | title": {"message": "Chỉ sử dụng vai trò của ARIA cho các phần tử tương thích"}, "core/audits/accessibility/aria-command-name.js | description": {"message": "<PERSON><PERSON> một phần tử không có tên thành phần hỗ trợ tiếp cận, trình đọc màn hình sẽ gọi phần tử đó bằng một tên gọi chung, dẫn đến việc người dùng trình đọc màn hình không sử dụng được phần tử này. [Tìm hiểu cách giúp các phần tử lệnh dễ tiếp cận hơn](https://dequeuniversity.com/rules/axe/4.10/aria-command-name)."}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> phần tử `button`, `link` và `menuitem` không có tên dễ đọc."}, "core/audits/accessibility/aria-command-name.js | title": {"message": "<PERSON><PERSON><PERSON> phần tử `button`, `link` và `menuitem` có tên dễ đọc"}, "core/audits/accessibility/aria-conditional-attr.js | description": {"message": "Một số thuộc tính ARIA chỉ có thể được sử dụng trên một phần tử trong những điều kiện nhất định. [Tìm hiểu thêm về các thuộc tính ARIA có điều kiện](https://dequeuniversity.com/rules/axe/4.10/aria-conditional-attr)."}, "core/audits/accessibility/aria-conditional-attr.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> thu<PERSON><PERSON> t<PERSON>h <PERSON> không đượ<PERSON> dùng theo chỉ định cho vai trò của phần tử"}, "core/audits/accessibility/aria-conditional-attr.js | title": {"message": "<PERSON><PERSON><PERSON> thuộ<PERSON> t<PERSON>h ARIA đ<PERSON><PERSON><PERSON> dùng theo chỉ định cho vai trò của phần tử"}, "core/audits/accessibility/aria-deprecated-role.js | description": {"message": "Công nghệ hỗ trợ có thể sẽ không xử lý đúng cách đối với các vai trò của ARIA mà Chrome không còn hỗ trợ. [Tìm hiểu thêm về các vai trò của ARIA mà Chrome không còn hỗ trợ](https://dequeuniversity.com/rules/axe/4.10/aria-deprecated-role)."}, "core/audits/accessibility/aria-deprecated-role.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> vai trò của ARIA mà Chrome không còn hỗ trợ đã được sử dụng"}, "core/audits/accessibility/aria-deprecated-role.js | title": {"message": "<PERSON><PERSON><PERSON> vai trò của ARIA mà Chrome không còn hỗ trợ không được sử dụng"}, "core/audits/accessibility/aria-dialog-name.js | description": {"message": "<PERSON><PERSON><PERSON> phần tử hộp tho<PERSON>i của ARIA không có tên thành phần hỗ trợ tiếp cận có thể khiến người dùng trình đọc màn hình không phân biệt được mục đích của các phần tử này. [Tìm hiểu cách giúp các phần tử hộp thoại của ARIA dễ tiếp cận hơn](https://dequeuniversity.com/rules/axe/4.10/aria-dialog-name)."}, "core/audits/accessibility/aria-dialog-name.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> phần tử có `role=\"dialog\"` hoặc `role=\"alertdialog\"` không có tên thành phần hỗ trợ tiếp cận."}, "core/audits/accessibility/aria-dialog-name.js | title": {"message": "<PERSON><PERSON><PERSON> phần tử có `role=\"dialog\"` hoặc `role=\"alertdialog\"` c<PERSON> tên thành phần hỗ trợ tiếp cận."}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "<PERSON><PERSON><PERSON> công nghệ hỗ trợ, chẳng hạn như trình đọc màn hình, sẽ hoạt động không nhất quán khi đặt `aria-hidden=\"true\"` trên tài liệu `<body>`. [Tìm hiểu ảnh hưởng của `aria-hidden` đối với phần nội dung của tài liệu](https://dequeuniversity.com/rules/axe/4.10/aria-hidden-body)."}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "Hi<PERSON>n c<PERSON>[aria-hidden=\"true\"]` trên tài li<PERSON>u `<body>`"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "Hi<PERSON>n không có `[aria-hidden=\"true\"]` trên tài li<PERSON> `<body>`"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "<PERSON><PERSON><PERSON> phần tử theo thứ tự giảm dần có thể lấy tiêu điểm bên trong một phần tử `[aria-hidden=\"true\"]` sẽ giúp ngăn người dùng công nghệ hỗ trợ, chẳng hạn như trình đọc màn hình, tiếp cận với các phần tử tương tác đó. [Tìm hiểu ảnh hưởng của `aria-hidden` đối với các phần tử có thể lấy tiêu điểm](https://dequeuniversity.com/rules/axe/4.10/aria-hidden-focus)."}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> phần tử `[aria-hidden=\"true\"]` c<PERSON> chứa các phần tử theo thứ tự giảm dần có thể lấy tiêu điểm"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "<PERSON><PERSON><PERSON> phần tử `[aria-hidden=\"true\"]` khô<PERSON> chứa các phần tử theo thứ tự giảm dần có thể lấy tiêu điểm"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "<PERSON><PERSON> một trường nhập dữ liệu không có tên thành phần hỗ trợ tiếp cận, thì trình đọc màn hình sẽ gọi trường đó bằng tên gọi chung, khiến người dùng trình đọc màn hình không dùng được trường này. [Tìm hiểu thêm về nhãn của trường nhập dữ liệu](https://dequeuniversity.com/rules/axe/4.10/aria-input-field-name)."}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> các trường nhập của ARIA ở trạng thái không thể tiếp cận"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "<PERSON><PERSON><PERSON> các trường nhập của ARIA ở trạng thái có thể tiếp cận"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "<PERSON><PERSON> một phần tử thước đo không có tên thành phần hỗ trợ tiếp cận, trình đọc màn hình sẽ gọi phần tử đó bằng một tên gọi chung, dẫn đến việc người dùng trình đọc màn hình không sử dụng được phần tử này. [Tìm hiểu cách đặt tên các phần tử `meter`](https://dequeuniversity.com/rules/axe/4.10/aria-meter-name)."}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> phần tử `meter` của ARIA không có tên dễ đọc."}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "<PERSON><PERSON><PERSON> phần tử `meter` của ARIA có tên dễ đọc"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "<PERSON><PERSON> một phần tử `progressbar` không có tên thành phần hỗ trợ tiếp cận, trình đọc màn hình sẽ gọi phần tử đó bằng một tên gọi chung, dẫn đến việc người dùng trình đọc màn hình không sử dụng được phần tử này. [Tìm hiểu cách gắn nhãn phần tử `progressbar`](https://dequeuniversity.com/rules/axe/4.10/aria-progressbar-name)."}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> phần tử `progressbar` c<PERSON><PERSON> ARIA không có tên dễ đọc."}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "<PERSON><PERSON><PERSON> phần tử `progressbar` c<PERSON><PERSON> ARIA có tên dễ đọc"}, "core/audits/accessibility/aria-prohibited-attr.js | description": {"message": "Việc sử dụng các thuộ<PERSON> tính ARIA ở vai trò bị cấm có thể khiến thông tin quan trọng không được truyền đạt tới những người dùng công nghệ hỗ trợ. [Tìm hiểu thêm về các vai trò bị cấm của ARIA](https://dequeuniversity.com/rules/axe/4.10/aria-prohibited-attr)."}, "core/audits/accessibility/aria-prohibited-attr.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> phần tử sử dụng thu<PERSON><PERSON> t<PERSON>h ARIA bị cấm"}, "core/audits/accessibility/aria-prohibited-attr.js | title": {"message": "<PERSON><PERSON><PERSON> phần tử chỉ sử dụng thuộ<PERSON> t<PERSON>h ARIA đư<PERSON><PERSON> phép"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "Một số vai trò ARIA có các thuộc tính bắt buộc mô tả trạng thái của phần tử cho trình đọc màn hình. [Tìm hiểu thêm về các vai trò và thuộc tính bắt buộc](https://dequeuniversity.com/rules/axe/4.10/aria-required-attr)."}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "`[role]` kh<PERSON><PERSON> có tất cả các thuộc t<PERSON>h `[aria-*]` b<PERSON>t buộc"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "`[role]` c<PERSON> tất cả các thuộc t<PERSON> `[aria-*]` b<PERSON>t buộc"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "Một số vai trò mẹ của Ứng dụng Internet phong phú dễ dùng (ARIA) phải chứa vai trò con cụ thể để thực hiện các chức năng hỗ trợ tiếp cận chủ định tương ứng. [Tìm hiểu thêm về các vai trò và phần tử con bắt buộc](https://dequeuniversity.com/rules/axe/4.10/aria-required-children)."}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> phần tử có `[role]` ARIA yêu cầu phần tử con phải chứa một `[role]` cụ thể hiện đang thiếu một số hoặc tất cả các phần tử con bắt buộc đó."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "<PERSON><PERSON><PERSON> phần tử có `[role]` ARIA yêu cầu phần tử con phải chứa một `[role]` cụ thể có tất cả các phần tử con bắt buộc."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "<PERSON><PERSON><PERSON> vai trò mẹ cụ thể phải chứa một số vai trò con của Ứng dụng Internet phong phú dễ dùng (ARIA) để thực hiện đúng cách các chức năng hỗ trợ tiếp cận chủ định tương ứng. [Tìm hiểu thêm về các vai trò của Ứng dụng Internet phong phú dễ dùng (ARIA) và phần tử mẹ bắt buộc](https://dequeuniversity.com/rules/axe/4.10/aria-required-parent)."}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "`[role]` kh<PERSON><PERSON> có trong phần tử mẹ bắt buộc tương <PERSON>ng"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "`[role]` c<PERSON> trong phần tử mẹ bắt buộc tương <PERSON>ng"}, "core/audits/accessibility/aria-roles.js | description": {"message": "<PERSON><PERSON><PERSON> vai trò của Ứng dụng Internet phong phú dễ dùng (ARIA) phải có giá trị hợp lệ để thực hiện những chức năng hỗ trợ tiếp cận chủ định tương ứng. [Tìm hiểu thêm về các vai trò của Ứng dụng Internet phong phú dễ dùng (ARIA)](https://dequeuniversity.com/rules/axe/4.10/aria-roles)."}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> giá trị của `[role]` l<PERSON> không hợp lệ"}, "core/audits/accessibility/aria-roles.js | title": {"message": "<PERSON><PERSON><PERSON> giá trị của `[role]` l<PERSON> hợp lệ"}, "core/audits/accessibility/aria-text.js | description": {"message": "N<PERSON>u bạn thêm `role=text` quanh một nút văn bản được phân tách bằng thẻ đánh dấu, VoiceOver có thể coi nút đó là một cụm từ, nhưng con cháu có thể làm tâm điểm của phần tử này sẽ không được công bố. [Tìm hiểu thêm về thuộc tính `role=text`](https://dequeuniversity.com/rules/axe/4.10/aria-text)."}, "core/audits/accessibility/aria-text.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> phần tử có thuộc tính `role=text` có con cháu có thể làm tâm điểm."}, "core/audits/accessibility/aria-text.js | title": {"message": "<PERSON><PERSON><PERSON> phần tử có thuộc tính `role=text` không có con cháu có thể làm tâm điểm."}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "<PERSON><PERSON> một trường chuyển đổi không có tên thành phần hỗ trợ tiếp cận, thì trình đọc màn hình sẽ gọi trường đó bằng tên gọi chung, khiến người dùng trình đọc màn hình không dùng được trường này. [Tìm hiểu thêm về trường chuyển đổi](https://dequeuniversity.com/rules/axe/4.10/aria-toggle-field-name)."}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> các trường chuyển đổi của ARIA ở trạng thái không thể tiếp cận"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "<PERSON><PERSON><PERSON> các trường chuyển đổi của ARIA ở trạng thái có thể tiếp cận"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "<PERSON><PERSON> một phần tử chú giải công cụ không có tên thành phần hỗ trợ tiếp cận, trình đọc màn hình sẽ gọi phần tử đó bằng một tên gọi chung, dẫn đến việc người dùng trình đọc màn hình không sử dụng được phần tử này. [Tìm hiểu cách đặt tên các phần tử `tooltip`](https://dequeuniversity.com/rules/axe/4.10/aria-tooltip-name)."}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> phần tử `tooltip` c<PERSON><PERSON> ARIA không có tên dễ đọc."}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "<PERSON><PERSON><PERSON> phần tử `tooltip` củ<PERSON> ARIA có tên dễ đọc"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "<PERSON><PERSON> một phần tử `treeitem` không có tên thành phần hỗ trợ tiếp cận, trình đọc màn hình sẽ gọi phần tử đó bằng một tên gọi chung, dẫn đến việc người dùng trình đọc màn hình không sử dụng được phần tử này. [Tìm hiểu thêm về cách gắn nhãn phần tử `treeitem`](https://dequeuniversity.com/rules/axe/4.10/aria-treeitem-name)."}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> phần tử `treeitem` của ARIA không có tên dễ đọc."}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "<PERSON><PERSON><PERSON> phần tử `treeitem` của ARIA có tên dễ đọc"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "<PERSON><PERSON><PERSON> công nghệ hỗ trợ, chẳng hạn như trình đọc màn hình, không thể diễn gi<PERSON>i thuộc tính của Ứng dụng Internet phong phú dễ dùng (ARIA) có giá trị không hợp lệ. [Tìm hiểu thêm về giá trị hợp lệ cho thuộc tính của Ứng dụng Internet phong phú dễ dùng (ARIA)](https://dequeuniversity.com/rules/axe/4.10/aria-valid-attr-value)."}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> trị của các thuộc t<PERSON>h `[aria-*]` là không hợp lệ"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "<PERSON><PERSON><PERSON> th<PERSON> `[aria-*]` có giá trị hợp lệ"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "<PERSON><PERSON><PERSON> công nghệ hỗ trợ, chẳng hạn như trình đọc màn hình, không thể diễn gi<PERSON>i các thuộc tính của Ứng dụng Internet phong phú dễ dùng (ARIA) có tên không hợp lệ. [Tìm hiểu thêm về các thuộc tính hợp lệ của Ứng dụng Internet phong phú dễ dùng (ARIA)](https://dequeuniversity.com/rules/axe/4.10/aria-valid-attr)."}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> th<PERSON> `[aria-*]` là không hợp lệ hoặc bị sai chính tả"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "<PERSON><PERSON><PERSON> `[aria-*]` là hợp lệ và không bị sai chính tả"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "<PERSON><PERSON><PERSON> ph<PERSON>n tử không đạt"}, "core/audits/accessibility/button-name.js | description": {"message": "<PERSON>hi một nút không có tên thành phần hỗ trợ tiếp cận, trình đọc màn hình sẽ thông báo đó là \"nút\", khiến người dùng trình đọc màn hình không sử dụng được nút này. [Tìm hiểu cách giúp các nút dễ tiếp cận hơn](https://dequeuniversity.com/rules/axe/4.10/button-name)."}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> nút không có tên có thể tiếp cận được"}, "core/audits/accessibility/button-name.js | title": {"message": "<PERSON><PERSON><PERSON> nút có tên tiếp cận đư<PERSON>c"}, "core/audits/accessibility/bypass.js | description": {"message": "Bằng việc thêm các cách bỏ qua nội dung lặp lại, người dùng bàn phím có thể khám phá trang một cách hiệu quả hơn. [Tìm hiểu thêm về cách tránh bị chặn](https://dequeuniversity.com/rules/axe/4.10/bypass)."}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "<PERSON><PERSON> này không chứa tiêu đề, đư<PERSON><PERSON> liên kết bỏ qua hoặc vùng mốc"}, "core/audits/accessibility/bypass.js | title": {"message": "<PERSON><PERSON> này chứa tiêu đề, phần tử liên kết bỏ qua hoặc vùng mốc"}, "core/audits/accessibility/color-contrast.js | description": {"message": "Nhiều người dùng gặp khó khăn khi đọc hoặc không thể đọc được văn bản có độ tương phản thấp. [Tìm hiểu cách cung cấp độ tương phản màu vừa đủ](https://dequeuniversity.com/rules/axe/4.10/color-contrast)."}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> nền trước và nền sau không có đủ tỷ lệ tương phản."}, "core/audits/accessibility/color-contrast.js | title": {"message": "<PERSON><PERSON><PERSON> nền trước và nền sau có đủ tỷ lệ tương phản"}, "core/audits/accessibility/definition-list.js | description": {"message": "<PERSON>hi danh sách định nghĩa không được đánh dấu đúng cách, thì trình đọc màn hình có thể tạo ra thông báo gây nhầm lẫn hoặc không chính xác. [Tìm hiểu cách định cấu trúc danh sách định nghĩa đúng cách](https://dequeuniversity.com/rules/axe/4.10/definition-list)."}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "`<dl>` không chỉ chứa các nhó<PERSON> `<dt>` và `<dd>` đư<PERSON>c sắp xếp đúng cách, các phần tử `<script>`, `<template>` hoặc `<div>`."}, "core/audits/accessibility/definition-list.js | title": {"message": "`<dl>` chỉ chứa các nh<PERSON> `<dt>` và `<dd>` đư<PERSON>c sắp xếp đúng cách, các phần tử `<script>`, `<template>` hoặc `<div>`."}, "core/audits/accessibility/dlitem.js | description": {"message": "<PERSON><PERSON><PERSON> mục trong danh sách định nghĩa (`<dt>` và `<dd>`) phải được đưa vào một phần tử `<dl>` mẹ để đảm bảo rằng trình đọc màn hình có thể thông báo đúng cách các mục này. [Tìm hiểu cách định cấu trúc danh sách định nghĩa đúng cách](https://dequeuniversity.com/rules/axe/4.10/dlitem)."}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> phần tử `<dl>` kh<PERSON><PERSON> bao gồm những mục trong danh sách định ngh<PERSON>a"}, "core/audits/accessibility/dlitem.js | title": {"message": "<PERSON><PERSON><PERSON> phần tử `<dl>` ch<PERSON><PERSON> mục trong danh sách định ngh<PERSON>a"}, "core/audits/accessibility/document-title.js | description": {"message": "Tiêu đề giúp người dùng trình đọc màn hình nắm được thông tin tổng quan về trang và giúp người dùng công cụ tìm kiếm chủ yếu dựa vào tiêu đề này xác định xem một trang có liên quan đến nội dung tìm kiếm của họ hay không. [Tìm hiểu thêm về tiêu đề tài liệu](https://dequeuniversity.com/rules/axe/4.10/document-title)."}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> li<PERSON>u không có phần tử `<title>`"}, "core/audits/accessibility/document-title.js | title": {"message": "<PERSON><PERSON><PERSON> li<PERSON>u có phần tử `<title>`"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "<PERSON><PERSON> các công nghệ hỗ trợ không bỏ qua các phiên bản khác, gi<PERSON> trị mã nhận dạng của Ứng dụng Internet phong phú dễ dùng (ARIA) không được trùng nhau. [Tìm hiểu cách khắc phục mã nhận dạng trùng lặp của Ứng dụng Internet phong phú dễ dùng (ARIA)](https://dequeuniversity.com/rules/axe/4.10/duplicate-id-aria)."}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "<PERSON>ã nhận dạng của ARIA bị trùng lặp"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "<PERSON>ã nhận dạng của Ứng dụng Internet phong phú dễ dùng (ARIA) là duy nhất"}, "core/audits/accessibility/empty-heading.js | description": {"message": "Tiêu đề không có nội dung hoặc có văn bản khó đọc sẽ khiến người dùng trình đọc màn hình không thể truy cập thông tin trên cấu trúc của trang. [Tìm hiểu thêm về tiêu đề](https://dequeuniversity.com/rules/axe/4.10/empty-heading)."}, "core/audits/accessibility/empty-heading.js | failureTitle": {"message": "Phần tử tiêu đề không chứa nội dung."}, "core/audits/accessibility/empty-heading.js | title": {"message": "Tất cả phần tử tiêu đề đều chứa nội dung."}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "<PERSON><PERSON><PERSON> công nghệ hỗ trợ, chẳng hạn như trình đọc màn hình, sử dụng nhãn đầu tiên, nhãn cuối cùng hoặc tất cả các nhãn có thể thông báo nhầm các trường biểu mẫu có nhiều nhãn. [Tìm hiểu cách sử dụng nhãn của biểu mẫu](https://dequeuniversity.com/rules/axe/4.10/form-field-multiple-labels)."}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> trường biểu mẫu có nhiều nhãn"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "<PERSON><PERSON><PERSON>ng có trường biểu mẫu nào có nhiều nhãn"}, "core/audits/accessibility/frame-title.js | description": {"message": "Người dùng trình đọc màn hình dựa vào tiêu đề khung để mô tả nội dung của khung. [Tìm hiểu thêm về tiêu đề khung](https://dequeuniversity.com/rules/axe/4.10/frame-title)."}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "<PERSON><PERSON>n tử `<frame>` hoặc `<iframe>` không có tiêu đề"}, "core/audits/accessibility/frame-title.js | title": {"message": "<PERSON><PERSON><PERSON> phần tử `<frame>` hoặc `<iframe>` có tiêu đề"}, "core/audits/accessibility/heading-order.js | description": {"message": "<PERSON><PERSON><PERSON> cách thức sắp xếp tiêu đề sao cho không bỏ qua các cấp độ thể hiện cấu trúc ngữ nghĩa của trang, người dùng dễ dàng hiểu được nội dung và thực hiện thao tác bằng công nghệ hỗ trợ hơn. [Tìm hiểu thêm về trình tự tiêu đề](https://dequeuniversity.com/rules/axe/4.10/heading-order)."}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> phần tử tiêu đề không tuân theo trình tự giảm dần"}, "core/audits/accessibility/heading-order.js | title": {"message": "<PERSON><PERSON><PERSON> phần tử tiêu đề xuất hiện theo trình tự giảm dần"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "Nếu một trang chưa chỉ định thuộc tính `lang`, thì trình đọc màn hình sẽ xem như trang đó đang hiển thị bằng ngôn ngữ mặc định mà người dùng chọn khi thiết lập trình đọc màn hình. Nếu trên thực tế, trang đó không hiển thị bằng ngôn ngữ mặc định, thì trình đọc màn hình có thể không thông báo văn bản của trang đó một cách chính xác. [Tìm hiểu thêm về thuộc tính `lang`](https://dequeuniversity.com/rules/axe/4.10/html-has-lang)."}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> tử `<html>` ch<PERSON>a có thuộc t<PERSON>h `[lang]`"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "<PERSON><PERSON>n tử `<html>` có thuộc t<PERSON> `[lang]`"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "Việc chỉ định [ngôn ngữ BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) hợp lệ sẽ giúp trình đọc màn hình thông báo văn bản đúng cách. [T<PERSON><PERSON> hiểu cách sử dụng thuộc tính `lang`](https://dequeuniversity.com/rules/axe/4.10/html-lang-valid)."}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> `[lang]` của phần tử `<html>` không có giá trị hợp lệ."}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> `[lang]` c<PERSON><PERSON> phần tử `<html>` có giá trị hợp lệ"}, "core/audits/accessibility/html-xml-lang-mismatch.js | description": {"message": "Nếu trang web không chỉ định một ngôn ngữ nhất quán thì trình đọc màn hình có thể không thông báo chính xác văn bản trong trang. [Tìm hiểu thêm về thuộc tính `lang`](https://dequeuniversity.com/rules/axe/4.10/html-xml-lang-mismatch)."}, "core/audits/accessibility/html-xml-lang-mismatch.js | failureTitle": {"message": "Phần tử `<html>` không sở hữu thuộc t<PERSON>h `[xml:lang]` có cùng ngôn ngữ cơ sở như thuộc tính `[lang]`."}, "core/audits/accessibility/html-xml-lang-mismatch.js | title": {"message": "Phần tử `<html>` sở hữu thuộc tính `[xml:lang]` có cùng ngôn ngữ cơ sở như thuộc tính `[lang]`."}, "core/audits/accessibility/identical-links-same-purpose.js | description": {"message": "<PERSON><PERSON><PERSON> đường liên kết có cùng một đích đến phải có cùng nội dung mô tả để giúp người dùng hiểu mục đích của đường liên kết và quyết định có nên truy cập hay không. [Tìm hiểu thêm về các đường liên kết giống hệt nhau](https://dequeuniversity.com/rules/axe/4.10/identical-links-same-purpose)."}, "core/audits/accessibility/identical-links-same-purpose.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> đường liên kết giống hệt nhau không có cùng mục đích."}, "core/audits/accessibility/identical-links-same-purpose.js | title": {"message": "<PERSON><PERSON><PERSON> đường liên kết giống hệt nhau có cùng mục đích."}, "core/audits/accessibility/image-alt.js | description": {"message": "<PERSON><PERSON><PERSON> phần tử thông tin nên là đoạn văn bản thay thế ngắn, có tính mô tả. C<PERSON> thể bỏ qua phần tử trang trí bằng một thuộc tính alt trống. [Tìm hiểu thêm về thuộc tính `alt`](https://dequeuniversity.com/rules/axe/4.10/image-alt)."}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "Phần tử hình <PERSON>nh không có thuộc tính `[alt]`"}, "core/audits/accessibility/image-alt.js | title": {"message": "<PERSON><PERSON><PERSON> phần tử hình ảnh có thuộc tính `[alt]`"}, "core/audits/accessibility/image-redundant-alt.js | description": {"message": "<PERSON><PERSON><PERSON> phần tử thông tin nên là đoạn văn bản thay thế ngắn, có tính mô tả. Văn bản thay thế giống hệt với văn bản bên cạnh đường liên kết hoặc hình ảnh có thể khiến người dùng trình đọc màn hình bối rối vì văn bản sẽ được đọc hai lần. [Tìm hiểu thêm về thuộc tính `alt`](https://dequeuniversity.com/rules/axe/4.10/image-redundant-alt)."}, "core/audits/accessibility/image-redundant-alt.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> phần tử hình ảnh có thuộc tính `[alt]` là văn bản thừa."}, "core/audits/accessibility/image-redundant-alt.js | title": {"message": "<PERSON><PERSON><PERSON> phần tử hình ảnh không có thuộc tính `[alt]` là văn bản thừa."}, "core/audits/accessibility/input-button-name.js | description": {"message": "Bạn nên thêm văn bản rõ ràng và dễ đọc vào các nút nhập để giúp người dùng trình đọc màn hình hiểu mục đích của nút nhập. [Tìm hiểu thêm về các nút nhập](https://dequeuniversity.com/rules/axe/4.10/input-button-name)."}, "core/audits/accessibility/input-button-name.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> n<PERSON> nh<PERSON>p không có văn bản rõ ràng."}, "core/audits/accessibility/input-button-name.js | title": {"message": "<PERSON><PERSON><PERSON> n<PERSON> nh<PERSON>p có văn bản rõ ràng."}, "core/audits/accessibility/input-image-alt.js | description": {"message": "<PERSON>hi dùng một hình ảnh làm nút `<input>`, thì việc cung cấp văn bản thay thế có thể giúp người dùng trình đọc màn hình hiểu rõ mục đích của nút đó. [Tìm hiểu về văn bản thay thế hình ảnh đầu vào](https://dequeuniversity.com/rules/axe/4.10/input-image-alt)."}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> thành phần `<input type=\"image\">` không có văn bản `[alt]`"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "<PERSON><PERSON><PERSON> thành phần `<input type=\"image\">` c<PERSON> văn bản `[alt]`"}, "core/audits/accessibility/label-content-name-mismatch.js | description": {"message": "<PERSON>h<PERSON>n văn bản hiển thị không khớp với tên thành phần hỗ trợ tiếp cận có thể gây ra trải nghiệm khó hiểu đối với người dùng trình đọc màn hình. [Tìm hiểu thêm về tên thành phần hỗ trợ tiếp cận](https://dequeuniversity.com/rules/axe/4.10/label-content-name-mismatch)."}, "core/audits/accessibility/label-content-name-mismatch.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> phần tử gắn nhãn văn bản hiển thị không có tên thành phần hỗ trợ tiếp cận trùng khớp."}, "core/audits/accessibility/label-content-name-mismatch.js | title": {"message": "Phần tử gắn nhãn văn bản hiển thị có tên thành phần hỗ trợ tiếp cận trùng khớp."}, "core/audits/accessibility/label.js | description": {"message": "<PERSON><PERSON><PERSON> nhãn đảm bảo rằng những công nghệ hỗ trợ, chẳng hạn như trình đọc màn hình, thông báo các biện pháp kiểm soát biểu mẫu đúng cách. [Tìm hiểu thêm về nhãn phần tử biểu mẫu](https://dequeuniversity.com/rules/axe/4.10/label)."}, "core/audits/accessibility/label.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> phần tử biểu mẫu không có nhãn liên kết"}, "core/audits/accessibility/label.js | title": {"message": "<PERSON><PERSON><PERSON> phần tử biểu mẫu có nhãn liên quan"}, "core/audits/accessibility/landmark-one-main.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> mốc ch<PERSON>h gi<PERSON>p người dùng trình đọc màn hình dễ dàng thao tác trên trang web. [Tìm hiểu thêm về điểm mốc](https://dequeuniversity.com/rules/axe/4.10/landmark-one-main)."}, "core/audits/accessibility/landmark-one-main.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> liệu không có điểm mốc ch<PERSON>h."}, "core/audits/accessibility/landmark-one-main.js | title": {"message": "<PERSON><PERSON><PERSON> liệu có một điểm mốc ch<PERSON>."}, "core/audits/accessibility/link-in-text-block.js | description": {"message": "Nhiều người dùng gặp khó khăn khi đọc hoặc không thể đọc được văn bản có độ tương phản thấp. Văn bản liên kết rõ ràng sẽ giúp người dùng có thị lực kém dễ đọc hơn. [Tìm hiểu cách giúp các đường liên kết dễ phân biệt](https://dequeuniversity.com/rules/axe/4.10/link-in-text-block)."}, "core/audits/accessibility/link-in-text-block.js | failureTitle": {"message": "Bạn cần dựa vào màu sắc để phân biệt các đường liên kết."}, "core/audits/accessibility/link-in-text-block.js | title": {"message": "Bạn có thể phân biệt được các đường liên kết mà không cần dựa vào màu sắc."}, "core/audits/accessibility/link-name.js | description": {"message": "Văn bản liên kết (và văn bản thay thế cho hình ảnh, khi dùng làm phần tử liên kết) có thể thấy rõ, là duy nhất và có thể lấy tiêu điểm sẽ cải thiện trải nghiệm khám phá cho người dùng trình đọc màn hình. [Tìm hiểu cách giúp các phần tử liên kết trở nên dễ tiếp cận](https://dequeuniversity.com/rules/axe/4.10/link-name)."}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> phần tử liên kết không có tên có thể nhận rõ"}, "core/audits/accessibility/link-name.js | title": {"message": "<PERSON><PERSON><PERSON> phần tử liên kết có tên có thể nhận rõ"}, "core/audits/accessibility/list.js | description": {"message": "<PERSON><PERSON><PERSON> trình đọc màn hình có các cách riêng để thông báo danh sách. Khi danh sách có cấu trúc phù hợp, trình đọc màn hình sẽ thông báo về danh sách chính xác hơn. [Tìm hiểu thêm về cấu trúc phù hợp của danh sách](https://dequeuniversity.com/rules/axe/4.10/list)."}, "core/audits/accessibility/list.js | failureTitle": {"message": "<PERSON><PERSON> sách không chỉ chứa các phần tử `<li>` và phần tử hỗ trợ tập lệnh (`<script>` và `<template>`)."}, "core/audits/accessibility/list.js | title": {"message": "<PERSON>h sách chỉ chứa các phần tử `<li>` và phần tử hỗ trợ tập lệnh (`<script>` và `<template>`)."}, "core/audits/accessibility/listitem.js | description": {"message": "Trình đọc màn hình yêu cầu các mục danh sách (`<li>`) phải có trong một `<ul>` `<ol>` hoặc `<menu>` mẹ để được thông báo đúng cách. [Tìm hiểu thêm về cấu trúc phù hợp của danh sách](https://dequeuniversity.com/rules/axe/4.10/listitem)."}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> mục trong danh sách (`<li>`) không có trong phần tử `<ul>` `<ol>` hoặc `<menu>` mẹ."}, "core/audits/accessibility/listitem.js | title": {"message": "<PERSON><PERSON><PERSON> mục trong danh sách (`<li>`) có trong phần tử mẹ `<ul>`, `<ol>` hoặc `<menu>`"}, "core/audits/accessibility/meta-refresh.js | description": {"message": "Người dùng không muốn một trang tự động làm mới và việc trang tự động làm mới sẽ chuyển tiêu điểm quay về đầu trang. Người dùng có thể cảm thấy khó chịu hoặc bị nhầm lẫn nếu gặp trường hợp này. [Tìm hiểu thêm về thẻ làm mới meta](https://dequeuniversity.com/rules/axe/4.10/meta-refresh)."}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> li<PERSON>u này sử dụng `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "<PERSON><PERSON><PERSON> li<PERSON>u này không dùng `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "Vi<PERSON><PERSON> tắt tính năng thu phóng sẽ gây trở ngại cho những người dùng có thị lực kém bị lệ thuộc vào chức năng phóng to màn hình để thấy rõ nội dung trang web. [Tìm hiểu thêm về thẻ meta cửa sổ xem](https://dequeuniversity.com/rules/axe/4.10/meta-viewport)."}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`[user-scalable=\"no\"]` đư<PERSON><PERSON> dùng trong phần tử `<meta name=\"viewport\">` hoặc thuộc tính `[maximum-scale]` nhỏ hơn 5."}, "core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]` không được sử dụng trong phần tử `<meta name=\"viewport\">` và thuộc t<PERSON>h `[maximum-scale]` không nhỏ hơn 5."}, "core/audits/accessibility/object-alt.js | description": {"message": "Trình đọc màn hình không dịch được nội dung không ở dạng văn bản. Bằng việc thêm văn bản thay thế vào các phần tử `<object>`, bạn có thể giúp trình đọc màn hình truyền đạt ý nghĩa cho người dùng. [Tìm hiểu thêm về văn bản thay thế cho `object` phần tử](https://dequeuniversity.com/rules/axe/4.10/object-alt)."}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> thành phần `<object>` không có văn bản thay thế"}, "core/audits/accessibility/object-alt.js | title": {"message": "<PERSON><PERSON><PERSON> thành phần `<object>` c<PERSON> văn bản thay thế"}, "core/audits/accessibility/select-name.js | description": {"message": "<PERSON><PERSON><PERSON> phần tử biểu mẫu không có nhãn hữu ích có thể khiến người dùng trình đọc màn hình cảm thấy khó chịu. [Tìm hiểu thêm về phần tử `select`](https://dequeuniversity.com/rules/axe/4.10/select-name)."}, "core/audits/accessibility/select-name.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> phần tử lựa chọn không có phần tử nhãn đi kèm."}, "core/audits/accessibility/select-name.js | title": {"message": "<PERSON><PERSON><PERSON> phần tử lựa chọn có phần tử nhãn liên kết."}, "core/audits/accessibility/skip-link.js | description": {"message": "<PERSON><PERSON>u có đường liên kết bỏ qua, người dùng có thể chuyển ngay đến nội dung chính để tiết kiệm thời gian. [Tìm hiểu thêm về đường liên kết bỏ qua](https://dequeuniversity.com/rules/axe/4.10/skip-link)."}, "core/audits/accessibility/skip-link.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> liên kết bỏ qua không thể làm tâm điểm."}, "core/audits/accessibility/skip-link.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> liên kết bỏ qua có thể làm tâm điểm."}, "core/audits/accessibility/tabindex.js | description": {"message": "G<PERSON><PERSON> trị lớn hơn 0 ngụ ý thứ tự điều hướng rõ ràng. Mặc dù hợp lệ về mặt kỹ thuật nhưng điều này thường tạo ra trải nghiệm khó chịu cho những người dùng bị lệ thuộc vào công nghệ hỗ trợ. [Tìm hiểu thêm về thuộc tính `tabindex`](https://dequeuniversity.com/rules/axe/4.10/tabindex)."}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "Một số phần tử có giá trị `[tabindex]` l<PERSON><PERSON> hơ<PERSON> 0"}, "core/audits/accessibility/tabindex.js | title": {"message": "<PERSON><PERSON><PERSON>ng phần tử nào có giá trị `[tabindex]` l<PERSON><PERSON> hơn 0"}, "core/audits/accessibility/table-duplicate-name.js | description": {"message": "<PERSON>hu<PERSON><PERSON> tính tóm tắt phải mô tả cấu trúc bảng, còn `<caption>` phải có tiêu đề trên màn hình. Việc đánh dấu bảng chính xác giúp ích cho người dùng trình đọc màn hình. [Tìm hiểu thêm về tóm tắt và chú thích](https://dequeuniversity.com/rules/axe/4.10/table-duplicate-name)."}, "core/audits/accessibility/table-duplicate-name.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> bảng có nội dung giống nhau trong thuộc tính tóm tắt và `<caption>.`"}, "core/audits/accessibility/table-duplicate-name.js | title": {"message": "<PERSON><PERSON><PERSON> bảng có nội dung khác nhau trong thuộc tính tóm tắt và `<caption>`."}, "core/audits/accessibility/table-fake-caption.js | description": {"message": "Trì<PERSON> đọc màn hình có các tính năng giúp người dùng dễ dàng sử dụng bảng hơn. <PERSON><PERSON><PERSON> bảng cần sử dụng phần tử chú thích thực tế thay vì các ô có thuộc tính `[colspan]` để người dùng trình đọc màn hình có trải nghiệm tốt hơn. [Tìm hiểu thêm về chú thích](https://dequeuniversity.com/rules/axe/4.10/table-fake-caption)."}, "core/audits/accessibility/table-fake-caption.js | failureTitle": {"message": "Bảng không sử dụng `<caption>` thay cho các ô có thuộc tính `[colspan]` để biểu thị chú thích."}, "core/audits/accessibility/table-fake-caption.js | title": {"message": "Bảng sử dụng `<caption>` thay vì các ô có thuộc t<PERSON>h `[colspan]` để biểu thị chú thích."}, "core/audits/accessibility/target-size.js | description": {"message": "<PERSON><PERSON><PERSON> chạm có kích thước và khoảng giãn cách phù hợp sẽ giúp người dùng kích hoạt mục tiêu nếu họ gặp khó khăn với việc chọn các thành phần điều khiển nhỏ. [Tìm hiểu thêm về đích chạm](https://dequeuniversity.com/rules/axe/4.10/target-size)."}, "core/audits/accessibility/target-size.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> chạm có kích thước hoặc khoảng giãn cách chưa phù hợp."}, "core/audits/accessibility/target-size.js | title": {"message": "<PERSON><PERSON><PERSON> chạm có kích thước và khoảng giãn cách phù hợp."}, "core/audits/accessibility/td-has-header.js | description": {"message": "Trình đọc màn hình có các tính năng giúp người dùng dễ dàng sử dụng bảng hơn. C<PERSON><PERSON> phần tử `<td>` trong bảng lớn (chiều rộng và chiều cao từ 3 ô trở lên) cần có tiêu đề bảng liên kết để người dùng trình đọc màn hình có trải nghiệm tốt hơn. [Tìm hiểu thêm về tiêu đề bảng](https://dequeuniversity.com/rules/axe/4.10/td-has-header)."}, "core/audits/accessibility/td-has-header.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> phần tử `<td>` trong `<table>` lớn không có tiêu đề bảng."}, "core/audits/accessibility/td-has-header.js | title": {"message": "<PERSON><PERSON><PERSON> phần tử `<td>` trong `<table>` lớn có một hoặc nhiều tiêu đề bảng."}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "Trình đọc màn hình có các tính năng giúp người dùng dễ dàng sử dụng bảng hơn. Việc đảm bảo các ô `<td>` sử dụng thuộc tính `[headers]` chỉ tham chiếu các ô khác trong cùng bảng có thể cải thiện trải nghiệm của người dùng trình đọc màn hình. [Tìm hiểu thêm về thuộc tính `headers`](https://dequeuniversity.com/rules/axe/4.10/td-headers-attr)."}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> ô trong phần tử `<table>` sử dụng thuộc tính `[headers]` tham chiếu đến một phần tử `id` không tìm thấy trong cùng một bảng."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "<PERSON><PERSON><PERSON> ô trong phần tử `<table>` sử dụng thuộc tính `[headers]` tham chiếu đến các ô trong cùng một bảng."}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "Trình đọc màn hình có các tính năng giúp người dùng dễ dàng sử dụng bảng hơn. Việc đảm bảo tiêu đề bảng luôn tham chiếu đến một nhóm ô nào đó có thể cải thiện trải nghiệm của người dùng trình đọc màn hình. [Tìm hiểu thêm về tiêu đề bảng](https://dequeuniversity.com/rules/axe/4.10/th-has-data-cells)."}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> phần tử `<th>` và phần tử có `[role=\"columnheader\"/\"rowheader\"]` không chứa các ô dữ liệu mà các phần tử đó mô tả."}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "<PERSON><PERSON><PERSON> phần tử `<th>` và phần tử có `[role=\"columnheader\"/\"rowheader\"]` ch<PERSON><PERSON> các ô dữ liệu mà các phần tử này mô tả."}, "core/audits/accessibility/valid-lang.js | description": {"message": "Việc chỉ định một [ngôn ngữ BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) hợp lệ cho các phần tử sẽ giúp trình đọc màn hình phát âm văn bản chính xác. [Tìm hiểu cách sử dụng thuộc tính `lang`](https://dequeuniversity.com/rules/axe/4.10/valid-lang)."}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> th<PERSON> `[lang]` không có giá trị hợp lệ"}, "core/audits/accessibility/valid-lang.js | title": {"message": "<PERSON><PERSON><PERSON> th<PERSON> `[lang]` có giá trị hợp lệ"}, "core/audits/accessibility/video-caption.js | description": {"message": "Khi video có chú thích, người dùng khiếm thính và người dùng bị suy giảm thính lực có thể tiếp cận thông tin trên video đó dễ dàng hơn. [Tìm hiểu thêm về chú thích của video](https://dequeuniversity.com/rules/axe/4.10/video-caption)."}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> phần tử `<video>` không chứa phần tử `<track>` có `[kind=\"captions\"]`."}, "core/audits/accessibility/video-caption.js | title": {"message": "<PERSON><PERSON><PERSON> phần tử `<video>` chứa phần tử `<track>` có `[kind=\"captions\"]`"}, "core/audits/autocomplete.js | columnCurrent": {"message": "<PERSON><PERSON><PERSON> trị hiện tại"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "<PERSON><PERSON> thông báo đề xuất"}, "core/audits/autocomplete.js | description": {"message": "`autocomplete` gi<PERSON><PERSON> người dùng gửi biểu mẫu nhanh hơn. <PERSON><PERSON> thuận tiện cho người dùng, hãy cân nhắc cho phép thuộc tính trên bằng cách đặt một giá trị hợp lệ cho `autocomplete`. [Tìm hiểu thêm về `autocomplete` trong các biểu mẫu](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)"}, "core/audits/autocomplete.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> tính `autocomplete` c<PERSON><PERSON> các phần tử `<input>` là không chính xác"}, "core/audits/autocomplete.js | manualReview": {"message": "<PERSON><PERSON><PERSON> cầu xem xét thủ công"}, "core/audits/autocomplete.js | reviewOrder": {"message": "<PERSON>em xét thứ tự của các mã thông báo"}, "core/audits/autocomplete.js | title": {"message": "<PERSON><PERSON><PERSON> phần tử `<input>` sử dụng `autocomplete` <PERSON><PERSON><PERSON> c<PERSON>ch"}, "core/audits/autocomplete.js | warningInvalid": {"message": "(<PERSON><PERSON><PERSON>) mã thông báo `autocomplete`: \"{token}\" là không hợp lệ trong {snippet}"}, "core/audits/autocomplete.js | warningOrder": {"message": "<PERSON>em xét thứ tự của các mã thông báo: \"{tokens}\" trong {snippet}"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "<PERSON><PERSON> thể thực hiện"}, "core/audits/bf-cache.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> thao tác di chuyển được thực hiện bằng cách quay lại trang trước hoặc tiếp tục tiến. Bộ nhớ đệm cho thao tác tiến/lùi (bfcache) có thể đẩy nhanh các thao tác quay lại này. [Tìm hiểu thêm về bfcache](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{1 lý do xảy ra lỗi}other{# lý do xảy ra lỗi}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "Lý do x<PERSON>y ra lỗi"}, "core/audits/bf-cache.js | failureTitle": {"message": "Trang ngăn chặn việc khôi phục bộ nhớ đệm cho thao tác tiến/lùi"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "Loại lỗi"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "<PERSON><PERSON><PERSON><PERSON> thể thực hiện"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "<PERSON><PERSON> chờ trình du<PERSON> hỗ trợ"}, "core/audits/bf-cache.js | title": {"message": "<PERSON>rang không ngăn việc khôi phục bộ nhớ đệm cho thao tác tiến/lùi"}, "core/audits/bf-cache.js | warningHeadless": {"message": "<PERSON><PERSON><PERSON><PERSON> thể kiểm tra bộ nhớ đệm cho thao tác tiến/lùi trong Headless Chrome cũ (`--chrome-flags=\"--headless=old\"`). <PERSON><PERSON> xem kết quả kiểm tra, h<PERSON><PERSON> sử dụng Headless Chrome mới (`--chrome-flags=\"--headless=new\"`) hoặc Chrome tiêu chuẩn."}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "<PERSON>ác tiện ích của Chrome ảnh hưởng tiêu cực đến hiệu suất tải của trang này. H<PERSON>y thử kiểm tra trang ở chế độ ẩn danh hoặc từ một hồ sơ trên Chrome không có tiện ích."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "<PERSON><PERSON><PERSON> gi<PERSON> tập l<PERSON>nh"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "<PERSON><PERSON> tích cú pháp tập lệnh"}, "core/audits/bootup-time.js | columnTotal": {"message": "<PERSON><PERSON>ng thời gian c<PERSON> CPU"}, "core/audits/bootup-time.js | description": {"message": "<PERSON><PERSON>y cân nhắc giảm thời gian dùng để phân tích cú pháp, biên soạn và thực thi JS. Bạn có thể giải quyết vấn đề này bằng cách phân phối các tải trọng JS nhỏ hơn. [Tìm hiểu cách giảm thời gian thực thi JavaScript](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)."}, "core/audits/bootup-time.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> thời gian thực thi JavaScript"}, "core/audits/bootup-time.js | title": {"message": "<PERSON>h<PERSON>i gian thực thi JavaScript"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "<PERSON><PERSON><PERSON> các mô-đun JavaScript lớn, trùng lặp khỏi gói để giảm số byte mà hoạt động mạng tiêu thụ một cách không cần thiết. "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "<PERSON><PERSON><PERSON> các mô-đun trùng lặp trong gói JavaScript"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Ảnh GIF lớn không có hiệu quả trong việc phân phối nội dung động. H<PERSON>y cân nhắc sử dụng video MPEG4/WebM cho ảnh động và PNG/WebP cho ảnh tĩnh thay vì ảnh GIF để tiết kiệm dữ liệu mạng. [Tìm hiểu thêm về định dạng video hiệu quả](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)"}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "<PERSON><PERSON> dụng các định dạng video cho nội dung động"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "Mã Polyfill và Transform cho phép trình duyệt cũ dùng các tính năng mới của JavaScript. <PERSON><PERSON>, nhiều tính năng không cần thiết cho trình duyệt hiện đại. Hãy cân nhắc sửa đổi quy trình xây dựng JavaScript để không chuyển đổi mã nguồn các tính năng [Đường cơ sở](https://web.dev/baseline), trừ phi bạn biết rằng mình phải hỗ trợ các trình duyệt cũ. [Tìm hiểu lý do hầu hết các trang web có thể triển khai mã ES6+ mà không cần chuyển đổi mã nguồn](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "core/audits/byte-efficiency/legacy-javascript.js | detectedCoreJs2Warning": {"message": "<PERSON><PERSON><PERSON> hiện phiên bản 2 của core-js trên trang này. <PERSON><PERSON><PERSON> nên nâng cấp lên phiên bản 3 để sử dụng nhiều cải tiến về hiệu suất."}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "Tr<PERSON>h phân phối JavaScript cũ tới các trình duyệt hiện đại"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "Các định dạng hình ảnh như WebP và AVIF thường nén tốt hơn so với các định dạng PNG hoặc JPEG. Điều này có nghĩa là tốc độ tải hình ảnh xuống sẽ nhanh hơn và tiêu tốn ít dữ liệu hơn. [Tìm hiểu thêm về định dạng hình ảnh mới](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)."}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "<PERSON><PERSON> phối hình ảnh ở định dạng mới và hiệu quả hơn"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "<PERSON><PERSON><PERSON> cân nhắc tải từng phần các hình ảnh ẩn và nằm ngoài màn hình sau khi tải xong tất cả tài nguyên quan trọng nhằm giảm thời gian tương tác. [Tìm hiểu cách trì hoãn hình ảnh ngoài màn hình](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)."}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "<PERSON><PERSON><PERSON> hoãn tải các hình <PERSON>nh ngoài màn hình"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "<PERSON><PERSON><PERSON> tài nguyên đang chặn lần hiển thị đầu tiên của trang. H<PERSON>y cân nhắc phân phối nội dòng JS/Biểu định kiểu xếp chồng (CSS) quan trọng và trì hoãn mọi JS/kiểu không quan trọng. [Tìm hiểu cách loại bỏ các tài nguyên chặn hiển thị](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)."}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Loại bỏ các tài nguyên chặn hiển thị"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Tải trọng mạng lớn gây tốn kém cho người dùng và thường khiến thời gian tải kéo dài. [Tìm hiểu cách giảm kích thước tải trọng](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)."}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "<PERSON><PERSON><PERSON> kích thước là {totalBytes, number, bytes} KiB"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> các tài nguyên lớn trên mạng"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "<PERSON>r<PERSON><PERSON> tài nguyên lớn trên mạng"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "Vi<PERSON><PERSON> giảm thiểu kích thước tệp Biểu định kiểu xếp chồng (CSS) có thể giảm kích thước tải trọng mạng. [Tìm hiểu cách giảm thiểu kích thước tệp Biểu định kiểu xếp chồng (CSS)](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)."}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "<PERSON><PERSON><PERSON> g<PERSON>n CSS"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Vi<PERSON><PERSON> giảm thiểu kích thước tệp JavaScript có thể giảm kích thước tải trọng và thời gian phân tích cú pháp tập lệnh. [Tìm hiểu cách giảm thiểu kích thước tệp JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)."}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "<PERSON><PERSON><PERSON> g<PERSON>n JavaScript"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "G<PERSON><PERSON><PERSON> những quy tắc không dùng đến trong các biểu định kiểu và trì hoãn CSS chưa sử dụng cho nội dung trong màn hình đầu tiên để giảm số byte mà hoạt động mạng sử dụng. [Tìm hiểu cách giảm CSS không dùng đến](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)."}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Giảm CSS không dùng đến"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Giảm JavaScript không dùng đến và trì hoãn việc tải các tập lệnh cho tới khi cần có các tập lệnh này để giảm số byte mà hoạt động mạng sử dụng. [Tìm hiểu cách giảm JavaScript không dùng đến](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)."}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Giảm JavaScript không dùng đến"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Bộ nhớ đệm có thời gian hữu dụng dài có thể giúp tăng tốc số lượt truy cập lặp lại vào trang của bạn. [Tìm hiểu thêm về chính sách bộ nhớ đệm hiệu quả](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)."}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{Đã tìm thấy 1 tài nguyên}other{Đã tìm thấy # tài nguyên}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "<PERSON><PERSON> phối các nội dung tĩnh bằng ch<PERSON>h sách bộ nhớ đệm hiệu quả"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Sử dụng ch<PERSON>h sách bộ nhớ đệm hiệu quả cho các nội dung tĩnh"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Hình ảnh được tối ưu hoá sẽ tải nhanh hơn và tiêu tốn ít dữ liệu di động hơn. [Tìm hiểu cách mã hoá hình ảnh hiệu quả](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)."}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "<PERSON><PERSON> hóa hình <PERSON>nh hiệu quả"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "<PERSON><PERSON><PERSON> th<PERSON> thực"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "<PERSON><PERSON><PERSON> th<PERSON><PERSON><PERSON> hiển thị"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> có kích thước lớn hơn kích thước hiển thị"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "<PERSON><PERSON><PERSON> có kích thư<PERSON>c hiển thị phù hợp"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "<PERSON><PERSON> phát hình ảnh có kích thước phù hợp để tiết kiệm dữ liệu di động và cải thiện thời gian tải. [Tìm hiểu cách định cỡ hình ảnh](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)."}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "<PERSON>hay đ<PERSON>i kích thư<PERSON><PERSON> hình <PERSON>nh cho phù hợp"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "Phải phân phát các tài nguyên dựa trên văn bản ở định dạng nén (gzip, deflate hoặc brotli) để giảm thiểu tổng số byte mạng. [Tìm hiểu thêm về việc nén văn bản](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)."}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "<PERSON><PERSON><PERSON> t<PERSON>h năng nén văn bản"}, "core/audits/clickjacking-mitigation.js | columnSeverity": {"message": "<PERSON><PERSON><PERSON> <PERSON> nghiêm trọng"}, "core/audits/clickjacking-mitigation.js | description": {"message": "Tiêu đề `X-Frame-Options` (XFO) hoặc lệnh `frame-ancestors` trong tiêu đề `Content-Security-Policy` (CSP) kiểm soát nơi có thể nhúng trang. Những tính năng này có thể giảm thiểu các cuộc tấn công clickjacking bằng cách chặn một số hoặc tất cả trang web nhúng trang đó. [Tìm hiểu thêm về cách giảm thiểu các cuộc tấn công clickjacking](https://developer.chrome.com/docs/lighthouse/best-practices/clickjacking-mitigation)."}, "core/audits/clickjacking-mitigation.js | noClickjackingMitigation": {"message": "<PERSON><PERSON><PERSON><PERSON> tìm thấy ch<PERSON>h sách kiểm soát khung"}, "core/audits/clickjacking-mitigation.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> thiểu các cuộc tấn công clickjacking bằng XFO hoặc CSP"}, "core/audits/critical-request-chains.js | description": {"message": "Các Chuỗi yêu cầu quan trọng dưới đây cho bạn biết những tài nguyên được tải ở mức độ ưu tiên cao. Hãy cân nhắc giảm độ dài chuỗi, gi<PERSON><PERSON> kích thước tài nguyên tải xuống hoặc trì hoãn tải xuống các tài nguyên không cần thiết để cải thiện tốc độ tải trang. [Tìm hiểu cách tránh tạo chuỗi yêu cầu quan trọng](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)."}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{Đã tìm thấy 1 chuỗi}other{Đã tìm thấy # chuỗi}}"}, "core/audits/critical-request-chains.js | title": {"message": "<PERSON>r<PERSON><PERSON> tạo chuỗi các yêu cầu quan trọng"}, "core/audits/csp-xss.js | columnDirective": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/csp-xss.js | columnSeverity": {"message": "<PERSON><PERSON><PERSON> <PERSON> nghiêm trọng"}, "core/audits/csp-xss.js | description": {"message": "Một <PERSON><PERSON> sách bảo mật nội dung (CSP) mạnh sẽ làm giảm đáng kể nguy cơ bị tấn công thông qua tập lệnh trên nhiều trang web (XSS). [Tìm hiểu cách sử dụng Ch<PERSON>h sách bảo mật nội dung (CSP) để ngăn chặn XSS](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)"}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "<PERSON>ú ph<PERSON>p"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "Trang này chứa một CSP được xác định trong thẻ `<meta>`. <PERSON><PERSON>y cân nhắc di chuyển CSP đó sang tiêu đề HTTP hoặc xác định một CSP nghiêm ngặt khác trong tiêu đề HTTP."}, "core/audits/csp-xss.js | noCsp": {"message": "<PERSON><PERSON><PERSON><PERSON> tìm thấy CSP nào ở chế độ thực thi"}, "core/audits/csp-xss.js | title": {"message": "<PERSON><PERSON><PERSON> b<PERSON>o CSP có khả năng chống lại hình thức tấn công thông qua XSS"}, "core/audits/deprecations.js | columnDeprecate": {"message": "<PERSON><PERSON><PERSON><PERSON> dùng n<PERSON>a/<PERSON><PERSON><PERSON> báo"}, "core/audits/deprecations.js | columnLine": {"message": "Dòng"}, "core/audits/deprecations.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> cùng, các API không dùng nữa sẽ bị xoá khỏi trình duyệt. [Tìm hiểu thêm về các API không dùng nữa](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)."}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{Tìm thấy 1 cảnh báo}other{Tìm thấy # cảnh báo}}"}, "core/audits/deprecations.js | failureTitle": {"message": "Sử dụng các API không dùng nữa"}, "core/audits/deprecations.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>c API không dùng nữa"}, "core/audits/dobetterweb/charset.js | description": {"message": "<PERSON><PERSON><PERSON> khai báo hệ thống mã hoá ký tự. Bạn có thể thực hiện việc này bằng thẻ `<meta>` trong 1024 byte đầu tiên của HTML hoặc trong tiêu đề phản hồi HTTP Loại–Nội dung. [Tìm hiểu thêm về cách khai báo hệ thống mã hoá ký tự](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)."}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> thông tin khai báo về bộ ký tự hoặc khai báo quá muộn trong HTML"}, "core/audits/dobetterweb/charset.js | title": {"message": "<PERSON><PERSON><PERSON> đ<PERSON>nh đúng bộ ký tự"}, "core/audits/dobetterweb/doctype.js | description": {"message": "<PERSON><PERSON> bạn chỉ định loại tài liệ<PERSON>, trình duy<PERSON>t sẽ không chuyển sang chế độ tương thích ngược. [Tìm hiểu thêm về việc khai báo loại tài liệu](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)."}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "Tên loại tài liệu phải là chuỗi `html`"}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "<PERSON><PERSON><PERSON> li<PERSON> ch<PERSON>a `doctype` kích ho<PERSON> `limited-quirks-mode`"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Tài liệu phải chứa một loại tài liệu"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "publicId lẽ ra phải là một chuỗi trống"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "systemId lẽ ra phải là một chuỗi trống"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "<PERSON><PERSON><PERSON> li<PERSON> ch<PERSON>a `doctype` k<PERSON>ch ho<PERSON> `quirks-mode`"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "Trang thiếu loại tài liệu HTML nên đã kích hoạt chế độ tương thích ngược"}, "core/audits/dobetterweb/doctype.js | title": {"message": "Trang có loại tài liệu HTML"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "<PERSON><PERSON><PERSON><PERSON> kê"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "<PERSON><PERSON><PERSON> trị"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "Một DOM lớn sẽ làm tăng mức sử dụng bộ nhớ, khiến [các phép tính về kiểu](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) k<PERSON><PERSON> dà<PERSON>, đồng thời tạo ra [các quy trình trình bày lại bố cục](https://developers.google.com/speed/articles/reflow) tốn kém. [Tìm hiểu cách tránh kích thước DOM quá lớn](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 phần tử}other{# phần tử}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>ch thư<PERSON> DOM quá lớn"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "<PERSON><PERSON> sâu DOM tối đa"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Tổng số các phần tử DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "<PERSON>ần tử con tối đa"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>ch thư<PERSON> DOM quá lớn"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Các trang web yêu cầu vị trí của người dùng mà không cung cấp ngữ cảnh sẽ khiến người dùng không tin tưởng hoặc bối rối. H<PERSON>y cân nhắc liên kết yêu cầu với hành động của người dùng. [Tìm hiểu thêm về quyền truy cập vị trí địa lý](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)."}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> c<PERSON>u quyền truy cập vị trí địa lý khi tải trang"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> yêu cầu quyền truy cập vị trí địa lý khi tải trang"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "<PERSON>ạ<PERSON> vấn đề"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "Các vấn đề được ghi vào bảng điều khiển `Issues` trong công cụ cho nhà phát triển của Chrome là những vấn đề chưa giải quyết. Những vấn đề này có thể do lỗi yêu cầu mạng, do không có đủ biện pháp kiểm soát bảo mật, cũng như do các vấn đề khác của trình duyệt gây ra. Hãy mở bảng điều khiển Vấn đề trong công cụ cho nhà phát triển của Chrome để biết thêm thông tin chi tiết về từng vấn đề."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> vấn đề được ghi vào bảng điều khiển `Issues` trong công cụ cho nhà phát triển của Chrome"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "B<PERSON> chặn theo ch<PERSON>h sách trên nhiều nguồn gốc"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "<PERSON><PERSON><PERSON><PERSON> cáo sử dụng nhiều tài nguyên"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "Không có vấn đề nào ở bảng điều khiển `Issues` trong công cụ cho nhà phát triển của Chrome"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "<PERSON><PERSON>t hiện tất cả thư viện JavaScript ở phía giao diện người dùng trên trang này. [Tìm hiểu thêm về kiểm tra chẩn đoán phát hiện thư viện JavaScript này](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)."}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "Thư viện JavaScript phát hiện được"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "Đ<PERSON>i với những người dùng có kết nối chậm, cá<PERSON> tập lệnh bên ngoài tự động được đưa vào qua `document.write()` có thể làm trang tải chậm hàng chục g<PERSON>. [Tìm hiểu cách tránh document.write()](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)."}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Tr<PERSON>h `document.write()`"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "Tr<PERSON>h `document.write()`"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "Các trang web yêu cầu gửi thông báo không có ngữ cảnh sẽ khiến người dùng không tin tưởng hoặc bối rối. Hãy cân nhắc liên kết yêu cầu với cử chỉ của người dùng. [Tìm hiểu thêm về việc xin cấp quyền gửi thông báo một cách có trách nhiệm](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)."}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> c<PERSON>u quyền truy cập thông báo khi tải trang"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> yêu cầu quyền truy cập thông báo khi tải trang"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "Việ<PERSON> không cho phép dán nội dung vào trường nhập dữ liệu sẽ ảnh hưởng xấu đến trải nghiệm người dùng và chặn các trình quản lý mật khẩu, khiến khả năng bảo mật suy yếu.[Tìm hiểu thêm về các trường nhập dữ liệu thân thiện với người dùng](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)."}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> người dùng dán nội dung vào các trường nhập dữ liệu"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "<PERSON> phép người dùng dán nội dung vào các trường nhập dữ liệu"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "<PERSON><PERSON><PERSON> th<PERSON>"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "So với HTTP/1.1 thì HTTP/2 mang lại nhiều lợi <PERSON>ch hơn, trong đó có khả năng đa hợp và tiêu đề nhị phân. [Tìm hiểu thêm về HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{Chưa phân phát 1 yêu cầu qua HTTP/2}other{Chưa phân phát # yêu cầu qua HTTP/2}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "Sử dụng HTTP/2"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "<PERSON><PERSON><PERSON> cân nhắc đánh dấu trình nghe sự kiện chạm và di chuyển con lăn là `passive` để cải thiện hiệu suất cuộn trên trang. [Tìm hiểu thêm về cách sử dụng trình nghe sự kiện thụ động](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)."}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Không sử dụng trình nghe bị động để cải thiện hiệu suất cuộn"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "<PERSON>ử dụng trình nghe bị động để cải thiện hiệu suất cuộn"}, "core/audits/errors-in-console.js | description": {"message": "Các lỗi được ghi vào bảng điều khiển là những sự cố chưa giải quyết. Những sự cố này có thể do lỗi yêu cầu mạng và các vấn đề khác của trình duyệt gây ra. [Tìm hiểu thêm về lỗi này trong thông tin kiểm tra chẩn đoán bảng điều khiển](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)"}, "core/audits/errors-in-console.js | failureTitle": {"message": "<PERSON><PERSON> ghi lỗi của trình duyệt vào bảng điều khiển"}, "core/audits/errors-in-console.js | title": {"message": "Chưa ghi lỗi nào của trình duyệt vào bảng điều khiển"}, "core/audits/font-display.js | description": {"message": "Tận dụng tính năng CSS `font-display` nhằm đảm bảo người dùng thấy được văn bản trong lúc phông chữ web đang tải. [Tìm hiểu thêm về `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/)."}, "core/audits/font-display.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> bảo văn bản vẫn hiển thị trong khi tải phông chữ web"}, "core/audits/font-display.js | title": {"message": "Tất cả văn bản vẫn hiển thị trong khi tải phông chữ web"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountFor<PERSON><PERSON><PERSON>,plural, =1{Lighthouse không thể tự động kiểm tra `font-display` giá trị cho nguồn gốc {fontOrigin}.}other{Lighthouse không thể tự động kiểm tra `font-display` giá trị cho nguồn gốc {fontOrigin}.}}"}, "core/audits/has-hsts.js | columnDirective": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/has-hsts.js | columnSeverity": {"message": "<PERSON><PERSON><PERSON> <PERSON> nghiêm trọng"}, "core/audits/has-hsts.js | description": {"message": "Việc triển khai tiêu đề HSTS giúp giảm đáng kể nguy cơ bị tấn công nghe lén và hạ cấp kết nối HTTP. Bạn nên triển khai theo từng giai đo<PERSON>, bắt đầu từ max-age (thời gian tối đa mà các phản hồi đã tìm nạp được phép dùng lại) tối đa thấp. [Tìm hiểu thêm về việc sử dụng chính sách HSTS mạnh.](https://developer.chrome.com/docs/lighthouse/best-practices/has-hsts)"}, "core/audits/has-hsts.js | invalidSyntax": {"message": "<PERSON><PERSON> pháp không hợp lệ"}, "core/audits/has-hsts.js | lowMaxAge": {"message": "`max-age` quá thấp"}, "core/audits/has-hsts.js | noHsts": {"message": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tiêu đề Bảo mật truyền tải nghiêm ngặt HTTP (HSTS)"}, "core/audits/has-hsts.js | noMaxAge": {"message": "<PERSON><PERSON><PERSON><PERSON> có lệnh `max-age`"}, "core/audits/has-hsts.js | noPreload": {"message": "<PERSON><PERSON><PERSON><PERSON> tìm thấy l<PERSON>nh `preload` nào"}, "core/audits/has-hsts.js | noSubdomain": {"message": "<PERSON><PERSON><PERSON><PERSON> tìm thấy lệnh `includeSubDomains` nào"}, "core/audits/has-hsts.js | title": {"message": "Sử dụng ch<PERSON>h sách HSTS mạnh"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "Tỷ l<PERSON> khung hình (Thực tế)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "Tỷ l<PERSON> khung hình (Hiển thị)"}, "core/audits/image-aspect-ratio.js | description": {"message": "<PERSON><PERSON><PERSON> thước hiển thị của hình ảnh phải khớp với tỷ lệ khung hình tự nhiên. [Tìm hiểu thêm về tỷ lệ khung hình của hình ảnh](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)."}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "<PERSON><PERSON>n thị hình ảnh có tỷ lệ khung hình không chính xác"}, "core/audits/image-aspect-ratio.js | title": {"message": "<PERSON><PERSON>n thị hình ảnh có tỷ lệ khung hình chính xác"}, "core/audits/image-size-responsive.js | columnActual": {"message": "<PERSON><PERSON><PERSON> th<PERSON> thực"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "<PERSON><PERSON><PERSON> th<PERSON><PERSON><PERSON> hiển thị"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "<PERSON><PERSON><PERSON> mong muốn"}, "core/audits/image-size-responsive.js | description": {"message": "<PERSON><PERSON><PERSON> thước tự nhiên của hình ảnh phải tỷ lệ với kích thước màn hình và tỷ lệ pixel để hình ảnh rõ nét nhất có thể. [Tìm hiểu cách cung cấp hình ảnh thích ứng](https://web.dev/articles/serve-responsive-images)."}, "core/audits/image-size-responsive.js | failureTitle": {"message": "<PERSON><PERSON> phối hình <PERSON>nh có độ phân giải thấp"}, "core/audits/image-size-responsive.js | title": {"message": "<PERSON><PERSON> phối hình <PERSON>nh có độ phân giải phù hợp"}, "core/audits/insights/cls-culprits-insight.js | columnScore": {"message": "<PERSON><PERSON><PERSON><PERSON> số về mức thay đổi bố cục"}, "core/audits/is-on-https.js | allowed": {"message": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>"}, "core/audits/is-on-https.js | blocked": {"message": "Bị chặn"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "URL không an toàn"}, "core/audits/is-on-https.js | columnResolution": {"message": "<PERSON><PERSON><PERSON> x<PERSON> lý yêu cầu"}, "core/audits/is-on-https.js | description": {"message": "Bạn cần bảo vệ tất cả trang web bằng HTTPS, kể cả những trang web không xử lý dữ liệu nhạy cảm. Điều này bao gồm tránh [nội dung hỗn hợp](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content), trong đó một số tài nguyên được tải qua HTTP cho dù yêu cầu ban đầu được gửi qua HTTPS. HTTPS ngăn kẻ xâm nhập can thiệp hoặc vô tình biết được nội dung trao đổi giữa ứng dụng và người dùng của bạn, đồng thời là điều kiện tiên quyết nếu bạn muốn dùng HTTP/2 và nhiều API nền tảng web mới. [Tìm hiểu thêm về HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)."}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{Tìm thấy 1 yêu cầu không an toàn}other{Tìm thấy # yêu cầu không an toàn}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "Không sử dụng HTTPS"}, "core/audits/is-on-https.js | title": {"message": "Sử dụng HTTPS"}, "core/audits/is-on-https.js | upgraded": {"message": "Đã tự động nâng cấp lên HTTPS"}, "core/audits/is-on-https.js | warning": {"message": "<PERSON><PERSON><PERSON><PERSON> phép nhưng có cảnh báo"}, "core/audits/largest-contentful-paint-element.js | columnPercentOfLCP": {"message": "% LCP (Nội dung lớn nhất hiển thị)"}, "core/audits/largest-contentful-paint-element.js | columnPhase": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/largest-contentful-paint-element.js | columnTiming": {"message": "<PERSON><PERSON><PERSON><PERSON> gian"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "<PERSON><PERSON><PERSON> là thành phần nội dung lớn nhất hiển thị trong khung nhìn. [Tìm hiểu thêm về thành phần Thời gian hiển thị nội dung lớn nhất](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/largest-contentful-paint-element.js | itemLoadDelay": {"message": "<PERSON><PERSON> tr<PERSON> khi tải"}, "core/audits/largest-contentful-paint-element.js | itemLoadTime": {"message": "<PERSON><PERSON><PERSON><PERSON> gian tải"}, "core/audits/largest-contentful-paint-element.js | itemRenderDelay": {"message": "<PERSON><PERSON> tr<PERSON> khi hiển thị"}, "core/audits/largest-contentful-paint-element.js | itemTTFB": {"message": "TTFB (Thời gian cho byte đầu tiên)"}, "core/audits/largest-contentful-paint-element.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> phần Th<PERSON>i gian hiển thị nội dung lớn nhất"}, "core/audits/layout-shifts.js | columnScore": {"message": "<PERSON><PERSON><PERSON><PERSON> số về mức thay đổi bố cục"}, "core/audits/layout-shifts.js | description": {"message": "Đ<PERSON><PERSON> là những thay đổi lớn nhất về bố cục được ghi nhận trên trang. Mỗi mục trong bảng đại diện cho một lần thay đổi bố cục và cho biết phần tử đã thay đổi nhiều nhất. Bên dưới mỗi mục là các nguyên nhân gốc có thể dẫn đến sự thay đổi bố cục. Một vài trong số những thay đổi về bố cục này có thể không được tính vào giá trị chỉ số CLS (Mức thay đổi bố cục tích luỹ) do phương pháp [kết xuất cửa sổ hiện tại](https://web.dev/articles/cls#what_is_cls). [Tìm hiểu cách cải thiện CLS (<PERSON><PERSON><PERSON> thay đổi bố cục tích luỹ)](https://web.dev/articles/optimize-cls)"}, "core/audits/layout-shifts.js | displayValueShiftsFound": {"message": "{shiftCount,plural, =1{<PERSON><PERSON> nhận thấy 1 lần thay đổi bố cục}other{Đã nhận thấy # lần thay đổi bố cục}}"}, "core/audits/layout-shifts.js | rootCauseFontChanges": {"message": "Phông chữ trên web đã tải"}, "core/audits/layout-shifts.js | rootCauseInjectedIframe": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>n"}, "core/audits/layout-shifts.js | rootCauseUnsizedMedia": {"message": "<PERSON>ần tử nội dung đa phương tiện không có kích thước rõ ràng"}, "core/audits/layout-shifts.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> các thay đổi lớn về bố cục"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "Hình ảnh trong màn hình đầu tiên được tải từng phần sẽ hiển thị sau trong vòng đời của trang, điều này có thể làm chậm thời gian hiển thị nội dung lớn nhất. [Tìm hiểu thêm về tính năng tải từng phần tối ưu](https://web.dev/articles/lcp-lazy-loading)."}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "<PERSON><PERSON><PERSON>nh <PERSON>hời gian hiển thị nội dung lớn nhất đã được tải từng phần"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "<PERSON><PERSON><PERSON>nh <PERSON>hời gian hiển thị nội dung lớn nhất không được tải từng phần"}, "core/audits/long-tasks.js | description": {"message": "Liệt kê những việc cần nhiều thời gian thực hiện nhất trong chuỗi chính. Thông tin này khá hữu ích trong việc xác định những thành phần có thời gian phản hồi tương tác chậm nhất. [Tìm hiểu cách tránh những việc cần nhiều thời gian thực hiện trong chuỗi chính](https://web.dev/articles/optimize-long-tasks)"}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{<PERSON><PERSON>t hiện thấy # việc cần nhiều thời gian thực hiện}other{Phát hiện thấy # việc cần nhiều thời gian thực hiện}}"}, "core/audits/long-tasks.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> những việc cần nhiều thời gian thực hiện trong chuỗi chính"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "<PERSON><PERSON>"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "<PERSON><PERSON><PERSON> cân nhắc giảm thời gian dùng để phân tích cú pháp, biên dịch và thực thi JS. Bạn có thể giải quyết vấn đề này bằng cách phân phối các tải trọng JS nhỏ hơn. [Tìm hiểu cách giảm thiểu công việc theo chuỗi chính](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)"}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> thiểu công việc theo chuỗi chính"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> thiểu công việc theo chuỗi chính"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "<PERSON>i<PERSON>m số tổng hợp về mức thay đổi bố cục đo lường mức độ dịch chuyển của các phần tử hiển thị trong khung nhìn. [Tìm hiểu thêm về chỉ số Điểm số tổng hợp về mức thay đổi bố cục](https://web.dev/articles/cls)."}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "Chỉ số Hiển thị nội dung đầu tiên đánh dấu thời điểm hiển thị văn bản hoặc hình ảnh đầu tiên. [Tìm hiểu thêm về chỉ số Hiển thị nội dung đầu tiên](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)."}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "Chỉ số Hiển thị nội dung đầu tiên đo lường thời điểm hiển thị nội dung chính của trang. [Tìm hiểu thêm về chỉ số Hiển thị nội dung đầu tiên](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)."}, "core/audits/metrics/interaction-to-next-paint.js | description": {"message": "Hoạt động tương tác với thời gian hiển thị tiếp theo đo lường khả năng phản hồi của trang, thời gian trang cần để phản hồi hoạt động đầu vào của người dùng một cách rõ ràng. [Tìm hiểu thêm về chỉ số Hoạt động tương tác với thời gian hiển thị tiếp theo](https://web.dev/articles/inp)."}, "core/audits/metrics/interactive.js | description": {"message": "Thời điểm tương tác là khoảng thời gian mà trang cần để trở nên hoàn toàn tương tác. [Tìm hiểu thêm về chỉ số Thời điểm tương tác](https://developer.chrome.com/docs/lighthouse/performance/interactive/)."}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "Thời gian hiển thị nội dung lớn nhất đánh dấu thời điểm hiển thị văn bản hoặc hình ảnh có kích thước lớn nhất. [Tìm hiểu thêm về chỉ số Thời gian hiển thị nội dung lớn nhất](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/metrics/max-potential-fid.js | description": {"message": "Thời gian phản hồi lần tương tác đầu tiên tối đa mà người dùng có thể gặp phải là thời gian thực hiện nhiệm vụ lâu nhất. [Tìm hiểu thêm về chỉ số Thời gian phản hồi lần tương tác đầu tiên tối đa có thể xảy ra](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)."}, "core/audits/metrics/speed-index.js | description": {"message": "Speed Index cho biết nội dung của một trang hiển thị nhanh chóng đến mức nào. [Tìm hiểu thêm về Speed Index](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)."}, "core/audits/metrics/total-blocking-time.js | description": {"message": "Tổng tất cả các khoảng thời gian giữa thời điểm Hiển thị nội dung đầu tiên (FCP) và Thời điểm tương tác khi thời gian của nhiệm vụ vượt quá 50 mili giây, đư<PERSON><PERSON> biểu thị bằng đơn vị mili giây. [Tìm hiểu thêm về chỉ số Tổng thời gian chặn](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)."}, "core/audits/network-rtt.js | description": {"message": "Thời gian trọn vòng (RTT) của mạng có ảnh hưởng lớn đến hiệu suất. Việ<PERSON> mất nhiều thời gian trọn vòng (RTT) để đến một nguồn gốc cho thấy máy chủ càng gần người dùng thì hiệu quả càng cao. [Tìm hiểu thêm về Thời gian trọn vòng](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "core/audits/network-rtt.js | title": {"message": "<PERSON>h<PERSON><PERSON> gian trọn vòng của mạng"}, "core/audits/network-server-latency.js | description": {"message": "<PERSON><PERSON> trễ của máy chủ có thể ảnh hưởng đến hiệu suất của trang web. Nếu máy chủ của một nguồn gốc có độ trễ cao, tức là máy chủ bị quá tải hoặc có hiệu suất phụ trợ thấp. [Tìm hiểu thêm về thời gian phản hồi của máy chủ](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "core/audits/network-server-latency.js | title": {"message": "<PERSON>h<PERSON><PERSON> gian dư<PERSON>i nền của máy chủ"}, "core/audits/non-composited-animations.js | description": {"message": "<PERSON><PERSON><PERSON> ảnh động không được ghép có thể kém chất lượng và làm tăng CLS (Điểm số tổng hợp về mức thay đổi bố cục). [Tìm hiểu cách tránh ảnh động không được ghép](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)"}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{Tìm thấy # phần tử ảnh động}other{Tìm thấy # phần tử ảnh động}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h liên quan đến bộ lọc có thể di chuyển pixel"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "<PERSON><PERSON><PERSON> tiêu có một ảnh động khác không tương thích"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "<PERSON><PERSON><PERSON><PERSON> có chế độ ghép không phải là \"replace\""}, "core/audits/non-composited-animations.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> dùng các ảnh động không đư<PERSON><PERSON>p"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "<PERSON><PERSON><PERSON><PERSON> tính liên quan đến biến đổi phụ thuộc vào kích thư<PERSON><PERSON> hộp"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{<PERSON><PERSON><PERSON>c tính CSS không được hỗ trợ: {properties}}other{<PERSON><PERSON><PERSON> thuộc tính CSS không được hỗ trợ: {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "<PERSON><PERSON><PERSON>ng có tham số thời gian không được hỗ trợ"}, "core/audits/origin-isolation.js | columnDirective": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/origin-isolation.js | columnSeverity": {"message": "<PERSON><PERSON><PERSON> <PERSON> nghiêm trọng"}, "core/audits/origin-isolation.js | description": {"message": "<PERSON><PERSON><PERSON> sách Cross-Origin-Opener-Policy (COOP) có thể được dùng để tách biệt cửa sổ cấp cao nhất với các tài liệu khác như cửa sổ bật lên. [Tìm hiểu thêm về việc triển khai tiêu đề COOP.](https://web.dev/articles/why-coop-coep#coop)"}, "core/audits/origin-isolation.js | invalidSyntax": {"message": "<PERSON><PERSON> pháp không hợp lệ"}, "core/audits/origin-isolation.js | noCoop": {"message": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tiêu đề COOP"}, "core/audits/origin-isolation.js | title": {"message": "<PERSON><PERSON><PERSON> b<PERSON>o tách biệt nguồn gốc theo cách phù hợp bằng COOP"}, "core/audits/preload-fonts.js | description": {"message": "Tải trước phông chữ có `optional` để khách truy cập lần đầu có thể sử dụng. [Tìm hiểu thêm về cách tải trước phông chữ](https://web.dev/articles/preload-optional-fonts)"}, "core/audits/preload-fonts.js | failureTitle": {"message": "Chưa tải trước phông chữ có `font-display: optional`"}, "core/audits/preload-fonts.js | title": {"message": "<PERSON><PERSON> tải trước phông chữ có `font-display: optional`"}, "core/audits/prioritize-lcp-image.js | description": {"message": "<PERSON><PERSON><PERSON> phần tử LCP được thêm vào trang qua phương thức động, bạn nên tải trước hình ảnh để cải thiện LCP. [Tìm hiểu thêm về cách tải trước phần tử LCP](https://web.dev/articles/optimize-lcp#optimize_when_the_resource_is_discovered)."}, "core/audits/prioritize-lcp-image.js | title": {"message": "<PERSON><PERSON><PERSON> trư<PERSON><PERSON> hình <PERSON>nh có Thời gian hiển thị nội dung lớn nhất"}, "core/audits/redirects-http.js | description": {"message": "Hãy nhớ chuyển hướng tất cả lưu lượng truy cập HTTP tới HTTPS để bật các tính năng web an toàn cho tất cả người dùng. [Tìm hiểu thêm](https://developer.chrome.com/docs/lighthouse/pwa/redirects-http/)."}, "core/audits/redirects-http.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> chuyển hướng lưu lượ<PERSON> truy cập HTTP tới HTTPS"}, "core/audits/redirects-http.js | title": {"message": "Chuyển hướ<PERSON> lưu lượ<PERSON> truy cập HTTP tới HTTPS"}, "core/audits/redirects.js | description": {"message": "<PERSON><PERSON><PERSON> lần chuyển hướng sẽ khiến tốc độ tải trang chậm thêm. [Tìm hiểu cách tránh các lần chuyển hướng trang](https://developer.chrome.com/docs/lighthouse/performance/redirects/)."}, "core/audits/redirects.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> chuyển hướng trang nhiều lần"}, "core/audits/seo/canonical.js | description": {"message": "Đường liên kết chính tắc đề xuất URL nào nên hiển thị trong kết quả tìm kiếm. [Tìm hiểu thêm về đường liên kết chính tắc](https://developer.chrome.com/docs/lighthouse/seo/canonical/)."}, "core/audits/seo/canonical.js | explanationConflict": {"message": "Nhiều URL xung đột ({urlList})"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "URL không hợp lệ ({url})."}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Trỏ đến một vị trí `hreflang` khác ({url})"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "<PERSON><PERSON><PERSON>ng phải là một URL tuyệt đối ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "Trỏ đến URL gốc của miền (trang chủ) thay vì trang nội dung tương đương"}, "core/audits/seo/canonical.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> li<PERSON>u không có `rel=canonical` hợp lệ"}, "core/audits/seo/canonical.js | title": {"message": "<PERSON><PERSON><PERSON> li<PERSON>u có `rel=canonical` hợp lệ"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "<PERSON><PERSON><PERSON><PERSON> liên kết không thể thu thập dữ liệu"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "Công cụ tìm kiếm có thể dùng các thuộc tính `href` trên đường liên kết để thu thập dữ liệu các trang web. Hãy đảm bảo rằng thuộc tính `href` của các phần tử neo liên kết đến một trang đích phù hợp để có thể phát hiện thêm nhiều trang khác của trang web. [Tìm hiểu cách khiến các đường liên kết cho phép thu thập thông tin](https://support.google.com/webmasters/answer/9112205)"}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> đ<PERSON>ờ<PERSON> liên kết không thể thu thập thông tin"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "<PERSON><PERSON><PERSON> đ<PERSON>ờ<PERSON> liên kết có thể thu thập thông tin"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "<PERSON><PERSON><PERSON> bả<PERSON> b<PERSON> sung khó đọc"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "Cỡ chữ"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "% văn bản trên trang"}, "core/audits/seo/font-size.js | columnSelector": {"message": "<PERSON><PERSON>"}, "core/audits/seo/font-size.js | description": {"message": "Cỡ chữ dưới 12 pixel là quá nhỏ và khó đọc, buộ<PERSON> khách truy cập trên thiết bị di động phải \"chụm để thu phóng\" mới đọc được. Tốt nhất là hơn 60% văn bản trên trang có cỡ chữ lớn hơn hoặc bằng 12 pixel. [Tìm hiểu thêm về cỡ chữ dễ đọc](https://developer.chrome.com/docs/lighthouse/seo/font-size/)."}, "core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} v<PERSON>n bản dễ đọc"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "V<PERSON>n bản không đọc được vì không có thẻ meta viewport nào được tối ưu hóa cho màn hình thiết bị di động."}, "core/audits/seo/font-size.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> li<PERSON>u không sử dụng cỡ chữ dễ đọc"}, "core/audits/seo/font-size.js | legibleText": {"message": "<PERSON><PERSON><PERSON> bản d<PERSON> đ<PERSON>c"}, "core/audits/seo/font-size.js | title": {"message": "<PERSON><PERSON><PERSON> liệu sử dụng cỡ chữ dễ đọc"}, "core/audits/seo/hreflang.js | description": {"message": "<PERSON><PERSON><PERSON> phần tử liên kết hreflang cho công cụ tìm kiếm biết nên liệt kê phiên bản trang nào trong kết quả tìm kiếm cho một ngôn ngữ hoặc khu vực cụ thể. [Tìm hiểu thêm về `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)."}, "core/audits/seo/hreflang.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> li<PERSON>u không có `hreflang` hợp lệ"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "<PERSON><PERSON><PERSON> trị href tư<PERSON><PERSON> đối"}, "core/audits/seo/hreflang.js | title": {"message": "<PERSON><PERSON><PERSON> li<PERSON>u có `hreflang` hợp lệ"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "<PERSON><PERSON> ngôn ngữ không mong muốn"}, "core/audits/seo/http-status-code.js | description": {"message": "<PERSON><PERSON><PERSON> trang có mã trạng thái HTTP không thành công có thể được lập chỉ mục không chính xác. [Tìm hiểu thêm về mã trạng thái HTTP](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)."}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "Trang có mã trạng thái HTTP không thành công"}, "core/audits/seo/http-status-code.js | title": {"message": "Trang có mã trạng thái HTTP thành công"}, "core/audits/seo/is-crawlable.js | description": {"message": "<PERSON><PERSON><PERSON> công cụ tìm kiếm không thể đưa trang của bạn vào kết quả tìm kiếm nếu bạn không cấp cho các công cụ này quyền thu thập thông tin của trang. [Tìm hiểu thêm về các lệnh của trình thu thập thông tin](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)."}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "<PERSON><PERSON> bị chặn lập chỉ mục"}, "core/audits/seo/is-crawlable.js | title": {"message": "<PERSON><PERSON> không bị chặn lập chỉ mục"}, "core/audits/seo/link-text.js | description": {"message": "<PERSON><PERSON><PERSON> bản mô tả trên đường liên kết giúp công cụ tìm kiếm hiểu được nội dung của bạn. [Tìm hiểu cách giúp các đường liên kết dễ tiếp cận hơn](https://developer.chrome.com/docs/lighthouse/seo/link-text/)."}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{Đã tìm thấy 1 liên kết}other{Đã tìm thấy # liên kết}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> phần tử liên kết không có văn bản mô tả"}, "core/audits/seo/link-text.js | title": {"message": "<PERSON><PERSON><PERSON> phần tử liên kết có văn bản mô tả"}, "core/audits/seo/manual/structured-data.js | description": {"message": "Chạy [<PERSON><PERSON><PERSON> cụ kiểm tra dữ liệu có cấu trúc](https://search.google.com/structured-data/testing-tool/) và [Công cụ khử lỗi dữ liệu có cấu trúc](http://linter.structured-data.org/) để xác thực loại dữ liệu này. [Tìm hiểu thêm về Dữ liệu có cấu trúc](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)."}, "core/audits/seo/manual/structured-data.js | title": {"message": "<PERSON><PERSON> liệu có cấu trúc là hợp lệ"}, "core/audits/seo/meta-description.js | description": {"message": "Bạn có thể thêm phần mô tả meta vào kết quả tìm kiếm để tóm tắt ngắn gọn nội dung trang. [Tìm hiểu thêm về phần mô tả meta](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)."}, "core/audits/seo/meta-description.js | explanation": {"message": "<PERSON><PERSON><PERSON> bản mô tả hiện đang trống."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> li<PERSON>u không có phần mô tả meta"}, "core/audits/seo/meta-description.js | title": {"message": "<PERSON><PERSON><PERSON> li<PERSON>u có phần mô tả meta"}, "core/audits/seo/robots-txt.js | description": {"message": "Nếu định dạng của tệp robots.txt không đúng, thì trình thu thập thông tin có thể không hiểu được cách bạn muốn thu thập thông tin hoặc lập chỉ mục trang web của mình. [Tìm hiểu thêm về tệp robots.txt](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)."}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "<PERSON><PERSON><PERSON> cầu robots.txt trả về trạng thái HTTP: {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{Đã tìm thấy 1 lỗi}other{Đã tìm thấy # lỗi}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse không tải đ<PERSON><PERSON> tệp robots.txt xuống"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "robots.txt không hợp lệ"}, "core/audits/seo/robots-txt.js | title": {"message": "<PERSON>.txt hợp lệ"}, "core/audits/server-response-time.js | description": {"message": "<PERSON><PERSON><PERSON> cho máy chủ phản hồi trong thời gian ngắn đối với tài liệu chính vì tất cả các yêu cầu khác phụ thuộc vào thời gian đó. [Tìm hiểu thêm về chỉ số Thời gian cho byte đầu tiên](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)."}, "core/audits/server-response-time.js | displayValue": {"message": "<PERSON><PERSON><PERSON> li<PERSON>u gốc mất {timeInMs, number, milliseconds} mili gi<PERSON>y"}, "core/audits/server-response-time.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> thời gian phản hồi ban đầu của máy chủ"}, "core/audits/server-response-time.js | title": {"message": "<PERSON>h<PERSON><PERSON> gian ph<PERSON>n hồi ban đầu của máy chủ là ngắn"}, "core/audits/third-party-cookies.js | description": {"message": "<PERSON><PERSON> của bên thứ ba có thể bị chặn trong một số trường hợp. [Tìm hiểu thêm về việc chuẩn bị cho các hạn chế đối với cookie của bên thứ ba](https://privacysandbox.google.com/cookies/prepare/overview)."}, "core/audits/third-party-cookies.js | displayValue": {"message": "{itemCount,plural, =1{Đã tìm thấy 1 cookie}other{Đã tìm thấy # cookie}}"}, "core/audits/third-party-cookies.js | failureTitle": {"message": "Sử dụng cookie của bên thứ ba"}, "core/audits/third-party-cookies.js | title": {"message": "<PERSON><PERSON><PERSON>h sử dụng cookie của bên thứ ba"}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (<PERSON><PERSON> thành công của khách hàng)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (<PERSON><PERSON><PERSON><PERSON> thị)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (<PERSON><PERSON> h<PERSON>)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (Video)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "<PERSON><PERSON><PERSON> p<PERSON>m"}, "core/audits/third-party-facades.js | description": {"message": "<PERSON><PERSON> thể tải từng phần một số nội dung nhúng của bên thứ ba. H<PERSON><PERSON> cân nhắc thay bằng một thành phần tượng trưng cho đến khi cần đến các nội dung này. [Tìm hiểu cách trì hoãn nội dung nhúng của bên thứ ba bằng thành phần tượng trưng](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)."}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{Có # thành phần thay thế dung lượng nhẹ}other{Có # thành phần thay thế dung lượng nhẹ}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "<PERSON><PERSON> thể tải từng phần một số tài nguyên của bên thứ ba bằng một thành phần dung lượng nhẹ"}, "core/audits/third-party-facades.js | title": {"message": "<PERSON><PERSON><PERSON> từng phần các tài nguyên của bên thứ ba bằng thành phần dung lượng nhẹ"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "<PERSON><PERSON><PERSON>ứ ba"}, "core/audits/third-party-summary.js | description": {"message": "Mã của bên thứ ba có thể tác động đáng kể đến hiệu suất tải. H<PERSON><PERSON> hạn chế số nhà cung cấp bên thứ ba dư thừa và cố gắng tải mã của bên thứ ba sau khi trang của bạn hoàn thành phần lớn quá trình tải. [Tìm hiểu cách giảm thiểu tác động từ mã của bên thứ ba](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "core/audits/third-party-summary.js | displayValue": {"message": "<PERSON>ã của bên thứ ba đã chặn chuỗi chính trong {timeInMs, number, milliseconds} mili giây"}, "core/audits/third-party-summary.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> mức ảnh hưởng của mã bên thứ ba"}, "core/audits/third-party-summary.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON><PERSON> mức sử dụng của bên thứ ba"}, "core/audits/trusted-types-xss.js | columnSeverity": {"message": "<PERSON><PERSON><PERSON> <PERSON> nghiêm trọng"}, "core/audits/trusted-types-xss.js | description": {"message": "Chỉ thị `require-trusted-types-for` trong tiêu đề `Content-Security-Policy` (CSP) hướng dẫn các tác nhân người dùng kiểm soát dữ liệu được truyền đến các hàm bồn lưu trữ dữ liệu có thể gây ra lỗ hổng XSS trong DOM. [Tìm hiểu thêm về cách giảm thiểu XSS dựa trên DOM bằng Trusted Types](https://developer.chrome.com/docs/lighthouse/best-practices/trusted-types-xss)."}, "core/audits/trusted-types-xss.js | noTrustedTypesToMitigateXss": {"message": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tiêu đề `Content-Security-Policy` có chỉ thị Trusted Types"}, "core/audits/trusted-types-xss.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> thiểu XSS dựa trên DOM bằng Trusted Types"}, "core/audits/unsized-images.js | description": {"message": "Đặt chiều rộng và chiều cao rõ ràng đối với các phần tử hình ảnh để giảm sự thay đổi về bố cục và cải thiện CLS (Đ<PERSON><PERSON><PERSON> số tổng hợp về mức thay đổi bố cục). [Tìm hiểu cách đặt kích thước hình ảnh](https://web.dev/articles/optimize-cls#images_without_dimensions)"}, "core/audits/unsized-images.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> phần tử hình ảnh không có `width` và `height` rõ ràng"}, "core/audits/unsized-images.js | title": {"message": "<PERSON><PERSON><PERSON> phần tử hình ảnh có `width` và `height` rõ ràng"}, "core/audits/user-timings.js | columnType": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/user-timings.js | description": {"message": "<PERSON><PERSON>y cân nhắc trang bị API Thời gian người dùng cho ứng dụng để đo lường hiệu suất thực tế của ứng dụng trong trải nghiệm người dùng chính. [Tìm hiểu thêm về các mốc Thời gian người dùng](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)."}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 dấu thời gian người dùng}other{# dấu thời gian người dùng}}"}, "core/audits/user-timings.js | title": {"message": "<PERSON><PERSON><PERSON> thời điểm cụ thể và khoảng thời gian được ghi lại bằng API Thời gian người dùng"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "Đã tìm thấy `<link rel=preconnect>` cho \"{securityOrigin}\" nhưng trình duyệt không sử dụng thuộc tính này. Hãy kiểm tra để đảm bảo rằng bạn đang sử dụng thuộc tính `crossorigin` đ<PERSON>g cách."}, "core/audits/uses-rel-preconnect.js | description": {"message": "<PERSON><PERSON><PERSON> cân nhắc thêm các gợi ý về tài nguyên `preconnect` hoặc `dns-prefetch` để thiết lập kết nối sớm tới những nguồn gốc quan trọng của bên thứ ba. [Tìm hiểu cách kết nối trước với những nguồn gốc bắt buộc](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)."}, "core/audits/uses-rel-preconnect.js | title": {"message": "<PERSON><PERSON><PERSON> n<PERSON>i trư<PERSON>c với các tên miền b<PERSON><PERSON> buộc"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "<PERSON><PERSON> phát hiện có hơn 2 đường kết nối `<link rel=preconnect>`. Bạn nên dùng những đường kết nối này một cách hợp lý và chỉ dùng cho các điểm gốc quan trọng nhất."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "Đã tìm thấy `<link rel=preconnect>` cho \"{securityOrigin}\" nhưng trình duyệt không sử dụng thuộc tính này. Chỉ sử dụng `preconnect` cho những điểm gốc quan trọng mà trang chắc chắn sẽ yêu cầu."}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Đã tìm thấy `<link>` tải trước cho \"{preloadURL}\" nhưng trình duyệt không sử dụng thuộc tính này. Hãy kiểm tra để đảm bảo rằng bạn đang sử dụng thuộc tính `crossorigin` đ<PERSON>g cách."}, "core/audits/uses-rel-preload.js | description": {"message": "<PERSON><PERSON><PERSON> cân nhắc sử dụng `<link rel=preload>` để sắp xếp thứ tự ưu tiên tìm nạp các tài nguyên đang được yêu cầu vào một thời điểm khác trong quá trình tải trang. [Tìm hiểu cách tải trước các yêu cầu ch<PERSON>](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)."}, "core/audits/uses-rel-preload.js | title": {"message": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> các yêu cầu ch<PERSON>h"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "URL của bản đ<PERSON>"}, "core/audits/valid-source-maps.js | description": {"message": "Bản đồ nguồn sẽ chuyển mã đã giảm thiểu thành mã nguồn gốc. Việc này giúp các nhà phát triển gỡ lỗi trong bản phát hành chính thức. <PERSON><PERSON><PERSON><PERSON> ra, Lighthouse cũng có thể cung cấp thêm thông tin chi tiết. Hãy cân nhắc triển khai bản đồ nguồn để tận dụng những lợi ích này. [Tìm hiểu thêm về bản đồ nguồn](https://developer.chrome.com/docs/devtools/javascript/source-maps/)."}, "core/audits/valid-source-maps.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> bản đồ nguồn cho JavaScript lớn của bên thứ nhất"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "Tệp JavaScript lớn thiếu bản đồ nguồn"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{Cảnh báo: thiếu 1 mục trong `.sourcesContent`}other{Cảnh báo: thiếu # mục trong `.sourcesContent`}}"}, "core/audits/valid-source-maps.js | title": {"message": "<PERSON><PERSON> có bản đồ nguồn hợp lệ"}, "core/audits/viewport.js | description": {"message": "`<meta name=\"viewport\">` không chỉ tối ưu hoá ứng dụng của bạn cho các kích thước màn hình trên thiết bị di động mà còn ngăn [độ trễ 300 mili giây xảy ra đối với hoạt động đầu vào của người dùng](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Tìm hiểu thêm về cách sử dụng thẻ meta cửa sổ xem](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)."}, "core/audits/viewport.js | explanationNoTag": {"message": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thẻ `<meta name=\"viewport\">`"}, "core/audits/viewport.js | failureTitle": {"message": "Không có thẻ `<meta name=\"viewport\">` có `width` hoặc `initial-scale`"}, "core/audits/viewport.js | title": {"message": "Có thẻ `<meta name=\"viewport\">` có `width` hoặc `initial-scale`"}, "core/audits/work-during-interaction.js | description": {"message": "<PERSON><PERSON><PERSON> là công việc chặn chuỗi xảy ra trong quá trình đo lường Hoạt động tương tác với thời gian hiển thị tiếp theo. [Tìm hiểu thêm về chỉ số Hoạt động tương tác với thời gian hiển thị tiếp theo](https://web.dev/articles/inp)."}, "core/audits/work-during-interaction.js | displayValue": {"message": "<PERSON><PERSON><PERSON><PERSON> thời gian dành cho sự kiện \"{interactionType}\" là {timeInMs, number, milliseconds} mili gi<PERSON>y"}, "core/audits/work-during-interaction.js | eventTarget": {"message": "<PERSON><PERSON><PERSON> ti<PERSON>u của sự kiện"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> thiểu công việc trong hoạt động tương tác ch<PERSON>h"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "<PERSON><PERSON> tr<PERSON> khi nhập thông tin"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "<PERSON><PERSON> tr<PERSON> khi trình bày"}, "core/audits/work-during-interaction.js | processingDuration": {"message": "<PERSON><PERSON><PERSON><PERSON> gian xử lý"}, "core/audits/work-during-interaction.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> thiểu công việc trong hoạt động tương tác ch<PERSON>h"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "<PERSON><PERSON><PERSON> là những cơ hội giúp cải thiện việc sử dụng ARIA trong ứng dụng của bạn, nhờ đó có thể nâng cao trải nghiệm cho người dùng công nghệ hỗ trợ, chẳng hạn như trình đọc màn hình."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "<PERSON><PERSON><PERSON> là các cơ hội để cung cấp nội dung thay thế cho âm thanh và video. <PERSON><PERSON><PERSON><PERSON> này có thể cải thiện trải nghiệm của người dùng khiếm thính hoặc khiếm thị."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Âm thanh và video"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "<PERSON><PERSON><PERSON><PERSON> mục này nêu bật các phương pháp hay nhất thường dùng cho hỗ trợ tiếp cận"}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Những phư<PERSON>ng pháp hay nhất"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "<PERSON><PERSON>c hoạt động kiểm tra này giúp xác định cơ hội [cải thiện khả năng hỗ trợ tiếp cận của ứng dụng web](https://developer.chrome.com/docs/lighthouse/accessibility/). Các hoạt động này chỉ có thể tự động phát hiện một phần vấn đề và không đảm bảo khả năng hỗ trợ tiếp cận của ứng dụng web. Vì vậy, bạn nên [kiểm tra cả theo cách thủ công](https://web.dev/articles/how-to-review)."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "<PERSON><PERSON><PERSON> mụ<PERSON> này nằm trong vùng không thể sử dụng công cụ kiểm tra tự động. Tìm hiểu thêm trong hướng dẫn của chúng tôi về cách [đánh giá khả năng hỗ trợ tiếp cận](https://web.dev/articles/how-to-review)."}, "core/config/default-config.js | a11yCategoryTitle": {"message": "Hỗ trợ tiếp cận"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "<PERSON><PERSON><PERSON> là các cơ hội gi<PERSON><PERSON> cải thiện độ dễ đọc cho nội dung của bạn."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "<PERSON><PERSON> tư<PERSON> phản"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "<PERSON><PERSON><PERSON> là những cơ hội giúp cải thiện khả năng diễn giải của người dùng đối với nội dung của bạn ở các ngôn ngữ khác nhau."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> tế hóa và bản địa hóa"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "<PERSON><PERSON><PERSON> là những cơ hội giúp cải thiện chức năng diễn giải ngữ nghĩa của các biện pháp kiểm soát trong ứng dụng của bạn. <PERSON><PERSON><PERSON>u này có thể nâng cao trải nghiệm cho những người dùng công nghệ hỗ trợ, chẳng hạn như trình đọc màn hình."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "<PERSON><PERSON>n và nhãn"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "<PERSON><PERSON><PERSON> là các cơ hội để cải thiện khả năng điều hướng bằng bàn phím trong ứng dụng của bạn."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "<PERSON><PERSON><PERSON> là những cơ hội để cải thiện trải nghiệm đọc dữ liệu trong bảng hoặc danh sách bằng công nghệ hỗ trợ, chẳng hạn như trình đọc màn hình."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "Bảng và danh sách"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "<PERSON><PERSON><PERSON> năng tư<PERSON><PERSON> thích của trình <PERSON>"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Phương ph<PERSON>p hay nhất"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "<PERSON>"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "Tin cậy và an toàn"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> nghiệm người dùng"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "Thông tin bổ sung về hiệu suất của ứng dụng. Những số liệu này không [trực tiếp ảnh hưởng](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) đến Điểm hiệu suất."}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "<PERSON><PERSON><PERSON> đ<PERSON>"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "<PERSON><PERSON><PERSON> cạnh quan trọng nhất của hiệu suất là tốc độ hiển thị pixel nhanh chóng trên màn hình. Các chỉ số chính: <PERSON><PERSON><PERSON> ảnh có nội dung đầu tiên, <PERSON><PERSON><PERSON> ảnh có ý nghĩa đầu tiên"}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "<PERSON><PERSON><PERSON> thao tác để cải thiện thời gian hiển thị hình ảnh đầu tiên"}, "core/config/default-config.js | insightsGroupDescription": {"message": "<PERSON><PERSON><PERSON>ng thông tin chi tiết này cũng có trong Bảng điều khiển hiệu suất của Chrome DevTools – hãy [ghi lại dấu vết](https://developer.chrome.com/docs/devtools/performance/reference) để xem thông tin chi tiết hơn."}, "core/config/default-config.js | insightsGroupTitle": {"message": "Th<PERSON>ng tin chi tiết"}, "core/config/default-config.js | metricGroupTitle": {"message": "Các chỉ số"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "<PERSON><PERSON>i thiện trải nghiệm tải tổng thể để trang phản hồi và sẵn sàng cho bạn sử dụng sớm nhất có thể. Các số liệu ch<PERSON>h: Thời điểm tương tác, Speed Index"}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "<PERSON><PERSON><PERSON> thao tác để cải thiện hiệu suất tổng thể"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "<PERSON><PERSON><PERSON>"}, "core/config/default-config.js | seoCategoryDescription": {"message": "Các hoạt động kiểm tra này đảm bảo rằng trang của bạn tuân theo lời khuyên cơ bản về cách tối ưu hoá cho công cụ tìm kiếm. Có nhiều yếu tố bổ sung mà Lighthouse không xếp hạng ở đây có thể ảnh hưởng đến thứ hạng của bạn trong kết quả tìm kiếm, kể cả hiệu suất của [Các chỉ số quan trọng về trang web](https://web.dev/explore/vitals). [Tìm hiểu thêm Kiến thức thiết yếu về Google Tìm kiếm](https://support.google.com/webmasters/answer/35769)."}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "Chạy những trình xác thực bổ sung này trên trang web của bạn để xem thêm các phương pháp hay nhất dành cho quy trình Tối ưu hóa cho công cụ tìm kiếm (SEO)."}, "core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "Định dạng HTML sao cho các trình thu thập thông tin có thể hiểu rõ hơn nội dung trong ứng dụng của bạn."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "<PERSON>hững phư<PERSON><PERSON> pháp hay nhất về nội dung"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "<PERSON><PERSON> xuất hiện trong kết quả tìm kiếm, c<PERSON><PERSON> trình thu thập thông tin cần quyền truy cập vào ứng dụng của bạn."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "<PERSON><PERSON> thập thông tin và lập chỉ mục"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "<PERSON><PERSON><PERSON> bảo các trang của bạn phù hợp với thiết bị di động để người dùng có thể đọc các trang nội dung mà không cần thu phóng. [Tìm hiểu cách điều chỉnh các trang cho phù hợp với thiết bị di động](https://developers.google.com/search/mobile-sites/)."}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "<PERSON><PERSON><PERSON> thiện với thiết bị di động"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "<PERSON><PERSON> vẻ như thiết bị được thử nghiệm có CPU chậm hơn mức Lighthouse mong đợi. Điều này có thể ảnh hưởng tiêu cực đến điểm hiệu suất của bạn. Tìm hiểu thêm về cách [hiệu chỉnh một hệ số giảm tốc CPU phù hợp](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)."}, "core/gather/driver/navigation.js | warningRedirected": {"message": "<PERSON>rang có thể không tải như dự kiến do URL kiểm tra của bạn ({requested}) đã được chuyển hướng tới {final}. <PERSON><PERSON><PERSON> thử kiểm tra trực tiếp URL thứ hai."}, "core/gather/driver/navigation.js | warningTimeout": {"message": "Trang này tải quá chậm nên không hoàn tất được trong giới hạn thời gian. <PERSON><PERSON><PERSON> kết quả có thể chưa hoàn chỉnh."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "<PERSON><PERSON> hết thời gian chờ xoá bộ nhớ đệm của trình duyệt. H<PERSON>y thử kiểm tra lại trang này và báo cáo lỗi nếu sự cố vẫn tiếp diễn."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{Dữ liệu đã lưu trữ có thể ảnh hưởng đến hiệu suất tải ở vị trí này: {locations}. Hãy kiểm tra trang này trong một cửa sổ ẩn danh để ngăn các tài nguyên đó ảnh hưởng đến điểm hiệu suất của bạn.}other{Dữ liệu đã lưu trữ có thể ảnh hưởng đến hiệu suất tải ở các vị trí này: {locations}. Hãy kiểm tra trang này trong một cửa sổ ẩn danh để ngăn các tài nguyên đó ảnh hưởng đến điểm hiệu suất của bạn.}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "<PERSON><PERSON> hết thời gian chờ xoá dữ liệu gốc. <PERSON><PERSON><PERSON> thử kiểm tra lại trang này và báo cáo lỗi nếu sự cố vẫn tiếp diễn."}, "core/gather/gatherers/link-elements.js | headerParseWarning": {"message": "<PERSON><PERSON> xảy ra lỗi khi phân tích cú pháp tiêu đề `link` ({error}): `{header}`"}, "core/gather/timespan-runner.js | warningNavigationDetected": {"message": "<PERSON><PERSON> phát hiện thao tác điều hướng trang trong quá trình chạy. Bạn không nên sử dụng chế độ khoảng thời gian để kiểm tra các thao tác điều hướng trang. Sử dụng chế độ điều hướng để kiểm tra các thao tác điều hướng trang nhằm cải thiện việc phát hiện luồng chính và thuộc tính của bên thứ ba."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "<PERSON><PERSON>y cân nhắc thêm giao thức URL https: và http: (bị các trình duyệt hỗ trợ `'strict-dynamic'` bỏ qua) để đảm bảo khả năng tương thích ngược với những trình duyệt cũ."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "<PERSON><PERSON> từ CSP3, `disown-opener` sẽ không được dùng nữa. <PERSON><PERSON> lòng sử dụng tiêu đề Cross-Origin-Opener-Policy."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "<PERSON><PERSON> từ CSP2, `referrer` sẽ không được dùng nữa. <PERSON><PERSON> lòng sử dụng tiêu đề Referrer-Policy."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "<PERSON><PERSON> từ CSP2, `reflected-xss` sẽ không được dùng nữa. <PERSON><PERSON> lòng sử dụng tiêu đề X-XSS-Protection."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "<PERSON><PERSON>u thiếu `base-uri`, hệ thống sẽ cho phép các thẻ `<base>` đư<PERSON><PERSON> chèn đặt URL cơ sở cho mọi URL tương đối (ví dụ: tập lệnh) thành miền do kẻ tấn công kiểm soát. Hãy cân nhắc đặt `base-uri` thành `'none'` hoặc `'self'`."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "<PERSON>hi thiếu `object-src`, hệ thống sẽ cho phép hành vi chèn các trình bổ trợ thực thi tập lệnh không an toàn. H<PERSON>y cân nhắc đặt `object-src` thành `'none'` nếu có thể."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "Thi<PERSON>u lệnh `script-src`. <PERSON><PERSON> không có lệnh nà<PERSON>, hệ thống có thể cho phép thực thi các tập lệnh không an toàn."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "Bạn quên dấu chấm phẩy ư? {keyword} có vẻ giống lệnh hơn là từ khóa."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "Nonces phải dùng bộ ký tự base64."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "Nonces phải có độ dài tối thiểu là 8 ký tự."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "<PERSON><PERSON><PERSON><PERSON> dùng các giao thức URL thuần túy ({keyword}) trong lệnh này. Gia<PERSON> thức URL thuần túy cho phép thực thi các tập lệnh từ miền không an toàn."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "Tr<PERSON><PERSON> dùng các ký tự đại diện thuần túy ({keyword}) trong lệnh này. Các ký tự đại diện thuần túy cho phép thực thi các tập lệnh từ miền không an toàn."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "<PERSON><PERSON><PERSON> b<PERSON>o cáo này chỉ được định cấu hình thông qua lệnh report-to. Chỉ các trình duyệt dựa trên Chromium mới hỗ trợ lệnh này. <PERSON>, bạn nên dùng cả lệnh `report-uri`."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "Kh<PERSON>ng có CSP nào định cấu hình đích báo cáo. Điều này sẽ gây khó khăn cho việc duy trì CSP theo thời gian và giám sát mọi sự cố."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "<PERSON><PERSON> sách máy chủ lưu trữ được cho phép có thể thường xuyên bị bỏ qua. <PERSON>hay vào đó, hãy cân nhắc dùng nonces hoặc hashes của CSP, cùng với `'strict-dynamic'` n<PERSON>u cần."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "Lệnh CSP không xác đ<PERSON>nh."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "<PERSON><PERSON> vẻ như {keyword} là một từ khóa không hợp lệ."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "`'unsafe-inline'` cho phép thực thi nhiều trình xử lý sự kiện và tập lệnh không an toàn trong trang. <PERSON><PERSON>y cân nhắc dùng nonces hoặc hashes của CSP để cho phép thực thi từng tập lệnh."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "H<PERSON>y cân nhắc thêm `'unsafe-inline'` (bị các trình duyệt hỗ trợ nonces/hashes bỏ qua) để đảm bảo khả năng tương thích ngược với những trình duyệt cũ."}, "core/lib/deprecation-description.js | feature": {"message": "<PERSON><PERSON><PERSON> tra trang trạng thái của tính năng để biết thêm thông tin."}, "core/lib/deprecation-description.js | milestone": {"message": "Thay đổi này sẽ có hiệu lực từ mốc {milestone}."}, "core/lib/deprecation-description.js | title": {"message": "<PERSON><PERSON> dùng một tính năng không dùng nữa"}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "Th<PERSON>i gian chặn chuỗi chính"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "<PERSON>h<PERSON><PERSON> gian tồn tạ<PERSON> (TTL) của bộ nhớ đệm"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "<PERSON><PERSON>i dung mô tả"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>"}, "core/lib/i18n/i18n.js | columnElement": {"message": "<PERSON><PERSON><PERSON> tử"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "<PERSON><PERSON><PERSON> ph<PERSON>n tử không đạt"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "<PERSON><PERSON> trí"}, "core/lib/i18n/i18n.js | columnName": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "<PERSON><PERSON> yêu cầu"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "<PERSON><PERSON><PERSON> th<PERSON> tài ng<PERSON>n"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "<PERSON><PERSON><PERSON> tài nguyên"}, "core/lib/i18n/i18n.js | columnSize": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnSource": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "<PERSON><PERSON><PERSON><PERSON> gian b<PERSON><PERSON> đầu"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "Thời gian sử dụng"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "<PERSON><PERSON><PERSON> c<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "<PERSON><PERSON><PERSON> tiết kiệm ư<PERSON> t<PERSON>h"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "<PERSON><PERSON><PERSON> tiết kiệm ư<PERSON> t<PERSON>h"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "<PERSON><PERSON><PERSON> tiết kiệm ước tính: {wastedBytes, number, bytes} KiB"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{Tìm thấy 1 phần tử}other{Tìm thấy # phần tử}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "<PERSON><PERSON><PERSON> tiết kiệm ước tính: {wastedMs, number, milliseconds} mili gi<PERSON>y"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "<PERSON><PERSON><PERSON> l<PERSON>"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "<PERSON><PERSON><PERSON>nh có ý nghĩa đầu tiên"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "Phông chữ"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "Interaction to Next Paint"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "<PERSON>"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "<PERSON>rung bình"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "<PERSON>h<PERSON><PERSON> gian ph<PERSON>n hồi lần tương tác đầu tiên tối đa có thể"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "<PERSON><PERSON><PERSON> dung nghe nhìn"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} mili giây"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "K<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "<PERSON><PERSON><PERSON> ng<PERSON>"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} gi<PERSON>y"}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "<PERSON><PERSON><PERSON><PERSON> định kiểu"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "<PERSON><PERSON><PERSON>ứ ba"}, "core/lib/i18n/i18n.js | total": {"message": "Tổng"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "Tổng"}, "core/lib/lh-error.js | badTraceRecording": {"message": "Đã xảy ra lỗi khi ghi dấu vết quá trình tải trang của bạn. Vui lòng chạy lại Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "<PERSON><PERSON> hết thời gian chờ kết nối với <PERSON> thức của trình gỡ lỗi ban đầu ban đầu."}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome không thu thập đ<PERSON><PERSON><PERSON> bất kỳ ảnh chụp màn hình nào trong quá trình tải trang. H<PERSON>y đảm bảo trang có nội dung hiển thị, sau đó thử chạy lại Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "Máy chủ DNS không thể phân giải miền đã cung cấp."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "<PERSON><PERSON><PERSON><PERSON> thu thập {artifactName} bắt buộc đã gặp lỗi: {errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "Đã xảy ra lỗi Chrome nội bộ. V<PERSON> lòng khởi động lại Chrome và thử chạy lại Lighthouse."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "<PERSON><PERSON><PERSON><PERSON> thu thập {artifactName} b<PERSON><PERSON> bu<PERSON><PERSON> không chạy."}, "core/lib/lh-error.js | noFcp": {"message": "Trang này chưa có nội dung nào. <PERSON><PERSON><PERSON> đảm bảo bạn hiển thị cửa sổ trình duyệt ở nền trước trong khi tải rồi thử lại. ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "<PERSON>rang không hiển thị nội dung đủ điều kiện là Thời gian hiển thị nội dung lớn nhất (LCP). <PERSON><PERSON><PERSON> đảm bảo trang có thành phần LCP hợp lệ rồi thử lại. ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "Trang đã cung cấp không phải là HTML (đ<PERSON><PERSON><PERSON> phân phối ở dạng loại MIME {mimeType})."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "<PERSON><PERSON>n bản Chrome này đã quá cũ nên không hỗ trợ \"{featureName}\". H<PERSON>y sử dụng phiên bản mới hơn để xem kết quả đầy đủ."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "Lighthouse không thể tải trang bạn đã yêu cầu một cách đáng tin cậy. H<PERSON>y đảm bảo bạn đang kiểm tra URL chính xác và máy chủ đang phản hồi tất cả các yêu cầu đúng cách."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "Lighthouse không thể tải URL bạn yêu cầu một cách đáng tin cậy vì trang này đã ngừng phản hồi."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "URL bạn cung cấp không có chứng chỉ bảo mật hợp lệ. {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome đã ngăn tải trang có quảng cáo xen kẽ. <PERSON><PERSON><PERSON> đảm bảo bạn đang kiểm tra URL chính xác và máy chủ đang phản hồi tất cả các yêu cầu đúng cách."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Lighthouse không thể tải trang bạn đã yêu cầu một cách đáng tin cậy. <PERSON><PERSON><PERSON> đảm bảo bạn đang kiểm tra URL chính xác và máy chủ đang phản hồi tất cả các yêu cầu đúng cách. (Chi tiết: {errorDetails})"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Lighthouse không thể tải trang bạn đã yêu cầu một cách đáng tin cậy. <PERSON><PERSON><PERSON> đảm bảo bạn đang kiểm tra URL chính xác và máy chủ đang phản hồi tất cả các yêu cầu đúng cách. (Mã trạng thái: {statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Thời gian tải trang quá lâu. <PERSON><PERSON><PERSON> thực hiện các khuyến nghị trong báo cáo để giảm thời gian tải trang rồi thử chạy lại Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "Quá trình chờ phản hồi của giao thức DevTools đã vượt quá thời gian phân bổ. (<PERSON>ư<PERSON><PERSON> thức: {protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "Quá trình tìm nạp nội dung tài nguyên đã vư<PERSON>t quá thời gian phân bổ"}, "core/lib/lh-error.js | targetCrashed": {"message": "Thẻ trình duyệt đã gặp sự cố bất ngờ."}, "core/lib/lh-error.js | urlInvalid": {"message": "URL bạn cung cấp có vẻ không hợp lệ."}, "core/lib/navigation-error.js | warningStatusCode": {"message": "Lighthouse không thể tải trang bạn đã yêu cầu một cách đáng tin cậy. <PERSON><PERSON><PERSON> đảm bảo bạn đang kiểm tra URL chính xác và máy chủ đang phản hồi tất cả các yêu cầu đúng cách. (Mã trạng thái: {errorCode})"}, "core/lib/navigation-error.js | warningXhtml": {"message": "Loại MIME của trang là XHTML: Lighthouse không hỗ trợ rõ ràng loại tài liệu này"}, "core/user-flow.js | defaultFlowName": {"message": "<PERSON><PERSON><PERSON> ng<PERSON>ời dùng ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "<PERSON><PERSON><PERSON> c<PERSON>o di chuyển trên trang ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> ch<PERSON> n<PERSON> ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "<PERSON><PERSON><PERSON> c<PERSON>o k<PERSON>ng thời gian ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "<PERSON><PERSON><PERSON> c<PERSON> báo cáo"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "<PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "Hỗ trợ tiếp cận"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "Phương ph<PERSON>p hay nhất"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "SEO"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "<PERSON><PERSON><PERSON> hi<PERSON> về Báo cáo luồng Lighthouse"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "<PERSON><PERSON><PERSON> v<PERSON>"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "Sử dụng chế độ Báo cáo di chuyển để…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "Sử dụng chế độ <PERSON><PERSON><PERSON> cáo ảnh chụp nhanh để..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "Sử dụng chế độ B<PERSON><PERSON> cáo khoảng thời gian để..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "Đ<PERSON><PERSON> đư<PERSON><PERSON> điểm Hiệu suất Lighthouse."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "<PERSON><PERSON> lường các chỉ số về Hiệu suất tải trang, chẳng hạn như Thời gian hiển thị nội dung lớn nhất và Speed Index."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "<PERSON><PERSON><PERSON> gi<PERSON> các chức năng của Ứng dụng web tiến bộ."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "<PERSON><PERSON><PERSON> các vấn đề về khả năng hỗ trợ tiếp cận trong các ứng dụng trang đơn hoặc các biểu mẫu phức tạp."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "<PERSON><PERSON><PERSON> giá các phương pháp hay nhất về trình đơn và các thành phần trên giao diện người dùng ẩn phía sau sự tương tác."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "<PERSON><PERSON> lường mức thay đổi bố cục và thời gian thực thi JavaScript trên một chuỗi các tương tác."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "<PERSON><PERSON>á<PERSON> phá các cơ hội về hiệu suất để cải thiện trải nghiệm cho những trang tồn tại lâu dài và các ứng dụng trang đơn."}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "<PERSON><PERSON><PERSON> động lớn nhất"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{{numInformative} quá trình kiểm tra cung cấp nhiều thông tin}other{{numInformative} quá trình kiểm tra cung cấp nhiều thông tin}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "<PERSON>"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "<PERSON><PERSON><PERSON> trang"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "Báo cáo di chuyển phân tích một lư<PERSON> tải trang, gi<PERSON><PERSON> hệt nh<PERSON> các báo cáo Lighthouse gốc."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "Báo c<PERSON>o di chuyển trên trang"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{{numNavigation} báo cáo điều hướng}other{{numNavigation} báo cáo điều hướng}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{{numPassableAudits} quá trình kiểm tra đạt đủ điều kiện}other{{numPassableAudits} quá trình kiểm tra đạt đủ điều kiện}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{{numPassed} quá trình kiểm tra đã đạt}other{{numPassed} quá trình kiểm tra đã đạt}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "<PERSON>rung bình"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "Lỗi"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "Tr<PERSON>ng thái đã chụp của trang"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "<PERSON><PERSON><PERSON> c<PERSON>o ảnh chụp nhanh phân tích trang ở một trạng thái cụ thể, thường là sau khi người dùng tương tác."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "<PERSON><PERSON>o c<PERSON>o tổng quan"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{{numSnapshot} báo cáo tổng quan nhanh}other{{numSnapshot} báo cáo tổng quan nhanh}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "<PERSON><PERSON> tương tác của người dùng"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "<PERSON><PERSON><PERSON> c<PERSON>o khoảng thời gian phân tích một khoảng thời gian bấ<PERSON> kỳ, thư<PERSON><PERSON> chứa các tương tác của người dùng."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "<PERSON><PERSON><PERSON> c<PERSON>o k<PERSON>ng thời gian"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{{numTimespan} báo cáo khoảng thời gian}other{{numTimespan} báo cáo khoảng thời gian}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "Báo cáo luồng người dùng Lighthouse"}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | AuthorizationCoveredByWildcard": {"message": "<PERSON>ý tự đại diện (*) sẽ không bao gồm việc uỷ quyền khi xử lýAccess-Control-Allow-Headers CORS."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "Nền sử dụng thuộc tính disableRemotePlaybacknhằm tắt chế độ tích hợp <PERSON> mặc định thay vì sử dụng bộ chọn -internal-media-controls-overlay-cast-button."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | CSSValueAppearanceSliderVertical": {"message": "<PERSON><PERSON><PERSON> trị của giao diện CSS slider-vertical không được chuẩn hoá và sẽ bị xoá."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | CanRequestURLHTTPContainingNewline": {"message": "<PERSON><PERSON><PERSON> yêu cầu tài nguyên có URL chứa cả ký tự \\(n|r|t) khoảng trắng đã bị xoá và ký tự nhỏ hơn (<) sẽ bị chặn. Vui lòng xoá các dòng mới và mã hoá ký tự nhỏ hơn khỏi các vị trí như giá trị thuộc tính phần tử để tải những tài nguyên như vậy."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes() không được dùng nữa; thay vào đ<PERSON>, hãy sử dụng API đã chuẩn hoá: Navigation Timing 2."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes() không được dùng nữa; thay vào đ<PERSON>, hãy sử dụng API đã chuẩn hoá: <PERSON><PERSON>."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes() không được dùng nữa; thay vào đ<PERSON>, h<PERSON>y sử dụng API đã chuẩn hoá: nextHopProtocol trong Navigation Timing 2."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | CookieWithTruncatingChar": {"message": "<PERSON><PERSON> có chứa ký tự \\(0|r|n) sẽ bị từ chối thay vì cắt bớt."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | CrossOriginAccessBasedOnDocumentDomain": {"message": "<PERSON><PERSON> thống không còn thiết lập document.domain để nới lỏng chính sách cùng nguồn gốc nữa và tính năng này sẽ bị tắt theo mặc định. Cảnh bảo về việc không dùng nữa này là dành cho quyền truy cập nhiều nguồn gốc đã được bật bằng cách thiết lập document.domain."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | CrossOriginWindowAlert": {"message": "<PERSON><PERSON><PERSON> năng kích hoạt window.alert qua các iframe trên nhiều nguồn gốc không được dùng nữa và sẽ bị loại bỏ trong tương lai."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | CrossOriginWindowConfirm": {"message": "<PERSON><PERSON><PERSON> năng kích hoạt window.confirm qua các iframe trên nhiều nguồn gốc không được dùng nữa và sẽ bị loại bỏ trong tương lai."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | DataUrlInSvgUse": {"message": "Hỗ trợ về dữ liệu: Các URL trong SVGUseElement không được dùng nữa và sẽ bị loại bỏ trong tương lai."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | GeolocationInsecureOrigin": {"message": "getCurrentPosition() và watchPosition() không còn hoạt động trên các nguồn gốc không an toàn. Đ<PERSON> dùng t<PERSON>h năng nà<PERSON>, bạn nên cân nhắc việc chuyển ứng dụng sang một nguồn gốc an toàn, chẳng hạn như HTTPS. Xem https://goo.gle/chrome-insecure-origins để biết thêm thông tin."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "getCurrentPosition() và watchPosition() không được dùng nữa trên các nguồn gốc không an toàn. Đ<PERSON> dùng tính năng nà<PERSON>, bạn nên cân nhắc việc chuyển ứng dụng sang một nguồn gốc an toàn, chẳng hạn như HTTPS. Xem https://goo.gle/chrome-insecure-origins để biết thêm thông tin."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | GetUserMediaInsecureOrigin": {"message": "getUserMedia() không còn hoạt động trên các nguồn gốc không an toàn. Đ<PERSON> dùng t<PERSON>h năng nà<PERSON>, bạn nên cân nhắc việc chuyển ứng dụng sang một nguồn gốc an toàn, chẳng hạn như HTTPS. Xem https://goo.gle/chrome-insecure-origins để biết thêm thông tin."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate không còn hoạt động. Thay vào đó, hãy sử dụng RTCPeerConnectionIceErrorEvent.address hoặc RTCPeerConnectionIceErrorEvent.port."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | IdentityInCanMakePaymentEvent": {"message": "<PERSON><PERSON><PERSON>n gốc của bên bán và dữ liệu bất kỳ trong sự kiện canmakepayment của trình chạy dịch vụ không được dùng nữa và sẽ bị loại bỏ: topOrigin, paymentRequestOrigin, methodData, modifiers."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | InsecurePrivateNetworkSubresourceRequest": {"message": "Trang web này đã yêu cầu một tài nguyên phụ qua một mạng mà trang web này chỉ truy cập được do có vị thế mạng đặc quyền của người dùng. Những yêu cầu như vậy làm lộ các thiết bị và máy chủ không công khai trên Internet, làm tăng nguy cơ bị tấn công giả mạo yêu cầu trên nhiều trang web (CSRF) và/hoặc rò rỉ thông tin. Để giảm thiểu những rủi ro này, Chrome sẽ ngừng yêu cầu các nguồn phụ không công khai khi được khởi tạo từ các ngữ cảnh không an toàn và sẽ bắt đầu chặn những yêu cầu đó."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | InterestGroupDailyUpdateUrl": {"message": "Trường dailyUpdateUrl của InterestGroups đư<PERSON>c truyền đến joinAdInterestGroup() đã được đổi tên thành updateUrl để phản ánh chính xác hơn hành vi của trường này."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator không đư<PERSON>c dùng nữa. <PERSON>hay v<PERSON><PERSON> đ<PERSON>, h<PERSON><PERSON> sử dụng Intl.Segmenter."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | LocalCSSFileExtensionRejected": {"message": "<PERSON>h<PERSON>ng tải được CSS qua các URL file: trừ phi chúng kết thúc bằng đuôi tệp .css."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | MediaSourceAbortRemove": {"message": "<PERSON><PERSON> thống không dùng SourceBuffer.abort() để huỷ việc xoá khoảng không đồng bộ của remove() nữa do thay đổi về quy cách. <PERSON>u này chế độ hỗ trợ cũng sẽ bị xoá. Thay vào đó, bạn nên nghe sự kiện updateend. <PERSON>ục đích của abort() chỉ là huỷ lệnh thêm nội dung nghe nhìn không đồng bộ (asynchronous media append) hoặc đặt lại trạng thái của trình phân tích cú pháp."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | MediaSourceDurationTruncatingBuffered": {"message": "Do thay đổi về quy cách, hệ thống không còn dùng chế độ đặt giá trị dưới dấu thời gian trình bày cao nhất cho MediaSource.duration nữa đối với mọi khung đã mã hoá và lưu vào vùng đệm. Chế độ hỗ trợ yêu cầu xoá tường minh cho nội dung phương tiện lưu trong bộ đệm bị cắt bớt sau này cũng sẽ bị xoá. Bạn nên triển khai lệnh remove(newDuration, oldDuration) tường minh trên mọi sourceBuffers, khi newDuration < oldDuration."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | NoSysexWebMIDIWithoutPermission": {"message": "Web MIDI sẽ yêu cầu cấp quyền sử dụng ngay cả khi MIDIOptions không chỉ rõ sysex."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | NotificationInsecureOrigin": {"message": "Có thể Notification API không được dùng nữa qua các nguồn gốc không an toàn. Bạn nên cân nhắc việc chuyển ứng dụng sang một nguồn gốc an toàn, chẳng hạn như HTTPS. Xem https://goo.gle/chrome-insecure-origins để biết thêm thông tin."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | NotificationPermissionRequestedIframe": {"message": "<PERSON><PERSON> thể quyền cho Notification API không còn được yêu cầu qua một iframe nhiều nguồn gốc nữa. Thay vào đó, bạn nên cân nhắc việc yêu cầu quyền qua một khung cấp cao hoặc mở một cửa sổ mới."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "<PERSON><PERSON> chọn imageOrientation: 'none' trong createImageBitmap không được dùng nữa. Thay v<PERSON><PERSON> đ<PERSON>, vui lòng sử dụng createImageBitmap bằng tuỳ chọn '\\\\{imageOrientation: 'from-image'\\\\}'."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | ObsoleteWebRtcCipherSuite": {"message": "<PERSON><PERSON><PERSON> tác của bạn đang đàm phán một phiên bản (D)TLS đã lỗi thời. Vui lòng trao đổi với đối tác của bạn để khắc phục."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | OverflowVisibleOnReplacedElement": {"message": "Việc chỉ định overflow: visible trên thẻ img, video và canvas có thể khiến các thẻ này tạo ra nội dung hình ảnh bên ngoài ranh giới thành phần. Xem tại https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | OverrideFlashEmbedwithHTML": {"message": "Mã nhúng video Flash cũ đã được viết lại thành iframe HTML. Flash đã ngừng hoạt động. Mẹo viết lại này không còn được dùng nữa và có thể bị loại bỏ trong tương lai."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | PaymentInstruments": {"message": "paymentManager.instruments không được dùng nữa. <PERSON><PERSON> lòng sử dụng chế độ cài đặt đúng thời điểm cho trình xử lý thanh toán."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | PaymentRequestCSPViolation": {"message": "Lệnh gọi PaymentRequest của bạn đã bỏ qua chỉ thị connect-src của <PERSON><PERSON> sách bảo mật nội dung (CSP). Chế độ bỏ qua này không được dùng nữa. <PERSON><PERSON> lòng thêm giá trị nhận dạng phương thức thanh toán từ API PaymentRequest (trong trường supportedMethods) vào chỉ thị connect-src của CSP."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | PersistentQuotaType": {"message": "StorageType.persistent không còn hoạt động. <PERSON>hay v<PERSON><PERSON> đ<PERSON>, vui lòng sử dụng navigator.storage đã chuẩn hoá."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | PictureSourceSrc": {"message": "<source src> c<PERSON> phần tử mẹ <picture> là không hợp lệ nên sẽ bị bỏ qua. Thay v<PERSON><PERSON> đ<PERSON>, vui lòng sử dụng <source srcset>."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame là phương thức có tiền tố nhà cung cấp. Thay v<PERSON><PERSON> đ<PERSON>, vui lòng sử dụng cancelAnimationFrame tiêu chuẩn."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame là phương thức có tiền tố nhà cung cấp. Thay v<PERSON><PERSON> đ<PERSON>, vui lòng sử dụng requestAnimationFrame tiêu chu<PERSON>n."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen không được dùng nữa. Thay vào đó, vui lòng sử dụng Document.fullscreenElement."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() không được dùng nữa. Thay vào đ<PERSON>, vui lòng sử dụng Element.requestFullscreen()."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() không được dùng nữa. Thay vào đ<PERSON>, vui lòng sử dụng Element.requestFullscreen()."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() không được dùng nữa. Thay v<PERSON><PERSON> đ<PERSON>, vui lòng sử dụng Document.exitFullscreen()."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() không được dùng nữa. Thay vào đ<PERSON>, vui lòng sử dụng Document.exitFullscreen()."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen không còn được dùng nữa. Thay vào đó, vui lòng sử dụng Document.fullscreenEnabled."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | PrivacySandboxExtensionsAPI": {"message": "<PERSON>úng tôi sẽ không dùng API chrome.privacy.websites.privacySandboxEnabled nữa. <PERSON><PERSON>, API này sẽ vẫn hoạt động nhằm đảm bảo khả năng tương thích ngược cho đến khi phát hành phiên bản M113. T<PERSON> vào đ<PERSON>, vui lòng sử dụng chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled và chrome.privacy.websites.adMeasurementEnabled. Truy cập vào https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | RTCConstraintEnableDtlsSrtpFalse": {"message": "<PERSON>iều kiện hạn chế DtlsSrtpKeyAgreement đã bị xoá. Bạn đã chỉ định một giá trị false cho điều kiện hạn chế này, đ<PERSON><PERSON> được coi là nỗ lực sử dụng phương thức SDES key negotiation đã bị xoá. Chức năng này đã bị xoá; thay vào đó, hãy sử dụng một dịch vụ có hỗ trợ DTLS key negotiation."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | RTCConstraintEnableDtlsSrtpTrue": {"message": "<PERSON>i<PERSON>u kiện hạn chế DtlsSrtpKeyAgreement đã bị xoá. Bạn đã chỉ định một giá trị true cho điều kiện hạn chế này, việc này không có tác dụng gì nhưng bạn có thể xoá điều kiện hạn chế này cho gọn gàng."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "Phương thức getStats() dựa trên lệnh gọi lại không được dùng nữa và sẽ bị loại bỏ. <PERSON><PERSON> v<PERSON><PERSON> đ<PERSON>, hãy sử dụng getStats() tuân thủ quy cách."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | RangeExpand": {"message": "Range.expand() không được dùng nữa. Thay vào đ<PERSON>, vui lòng sử dụng Selection.modify()."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | RequestedSubresourceWithEmbeddedCredentials": {"message": "<PERSON><PERSON><PERSON> cầu tài nguyên phụ có URL chứa thông tin xác thực đượ<PERSON> nhún<PERSON> (ví dụ: **********************/) sẽ bị chặn."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | RtcpMuxPolicyNegotiate": {"message": "Tuỳ chọn rtcpMuxPolicy không được dùng nữa và sẽ bị xoá."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer sẽ đòi hỏi việc tách biệt nhiều nguồn gốc. Xem https://developer.chrome.com/blog/enabling-shared-array-buffer/ để biết thêm thông tin."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | TextToSpeech_DisallowedByAutoplay": {"message": "<PERSON><PERSON> không có hoạt động của người dùng, speechSynthesis.speak() không được dùng nữa và sẽ được loại bỏ."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | UnloadHandler": {"message": "<PERSON><PERSON><PERSON><PERSON> nghe sự kiện huỷ tải không dùng được nữa và sẽ bị loại bỏ."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "<PERSON><PERSON><PERSON> tiện ích phải chọn sử dụng chế độ tách biệt nhiều nguồn gốc đẻ có thể tiếp tục sử dụng SharedArrayBuffer. Xem https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | XHRJSONEncodingDetection": {"message": "JSON phản hồi không hỗ trợ UTF-16 trong XMLHttpRequest"}, "node_modules/@paulirish/trace_engine/generated/Deprecation.js | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "T<PERSON>h năng XMLHttpRequest đồng bộ trên luồng chính không được dùng nữa do có tác động không tốt đối với trải nghiệm của người dùng cuối. <PERSON><PERSON> xem thêm thông tin trợ giúp, h<PERSON><PERSON> tham kh<PERSON>o https://xhr.spec.whatwg.org/."}, "node_modules/@paulirish/trace_engine/models/trace/insights/CLSCulprits.js | animation": {"message": "Ảnh động"}, "node_modules/@paulirish/trace_engine/models/trace/insights/CLSCulprits.js | description": {"message": "<PERSON><PERSON> cục thay đổi khi phần tử di chuyển mà không có sự tương tác của người dùng. [Hãy tìm hiểu nguyên nhân bố cục thay đổi](https://web.dev/articles/optimize-cls), chẳng hạn như phần tử được thêm, bị xoá hoặc phông chữ của phần tử thay đổi khi trang tải."}, "node_modules/@paulirish/trace_engine/models/trace/insights/CLSCulprits.js | injectedIframe": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>n"}, "node_modules/@paulirish/trace_engine/models/trace/insights/CLSCulprits.js | layoutShiftCluster": {"message": "<PERSON><PERSON><PERSON> thay đổi bố cục @ {PH1}"}, "node_modules/@paulirish/trace_engine/models/trace/insights/CLSCulprits.js | noCulprits": {"message": "<PERSON><PERSON><PERSON><PERSON> phát hiện ra nguyên nhân thay đổi bố cục"}, "node_modules/@paulirish/trace_engine/models/trace/insights/CLSCulprits.js | noLayoutShifts": {"message": "<PERSON><PERSON><PERSON><PERSON> có sự thay đổi bố cục"}, "node_modules/@paulirish/trace_engine/models/trace/insights/CLSCulprits.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> nhân làm thay đổi bố cục"}, "node_modules/@paulirish/trace_engine/models/trace/insights/CLSCulprits.js | topCulprits": {"message": "<PERSON><PERSON><PERSON><PERSON> nhân làm thay đổi bố cục thường gặp nhất"}, "node_modules/@paulirish/trace_engine/models/trace/insights/CLSCulprits.js | unsizedImage": {"message": "<PERSON><PERSON><PERSON><PERSON> phần hình <PERSON>nh chưa xác định kích thước"}, "node_modules/@paulirish/trace_engine/models/trace/insights/CLSCulprits.js | webFont": {"message": "Phông chữ web"}, "node_modules/@paulirish/trace_engine/models/trace/insights/CLSCulprits.js | worstCluster": {"message": "<PERSON><PERSON><PERSON> t<PERSON> nhất"}, "node_modules/@paulirish/trace_engine/models/trace/insights/CLSCulprits.js | worstLayoutShiftCluster": {"message": "<PERSON><PERSON><PERSON> thay đổi bố cục tệ nhất"}, "node_modules/@paulirish/trace_engine/models/trace/insights/Cache.js | cacheTTL": {"message": "<PERSON>h<PERSON><PERSON> gian tồn tạ<PERSON> (TTL) của bộ nhớ đệm"}, "node_modules/@paulirish/trace_engine/models/trace/insights/Cache.js | description": {"message": "Bộ nhớ đệm có thời gian hữu dụng dài có thể giúp tăng tốc số lượt truy cập lặp lại vào trang của bạn. [Tìm hiểu thêm](https://web.dev/uses-long-cache-ttl/)"}, "node_modules/@paulirish/trace_engine/models/trace/insights/Cache.js | noRequestsToCache": {"message": "<PERSON>h<PERSON>ng yêu cầu nào có ch<PERSON>h sách bộ nhớ đệm không hiệu quả"}, "node_modules/@paulirish/trace_engine/models/trace/insights/Cache.js | others": {"message": "{PH1} mục kh<PERSON>c"}, "node_modules/@paulirish/trace_engine/models/trace/insights/Cache.js | requestColumn": {"message": "<PERSON><PERSON><PERSON> c<PERSON>"}, "node_modules/@paulirish/trace_engine/models/trace/insights/Cache.js | title": {"message": "<PERSON><PERSON> dụng thời gian hữu dụng của bộ nhớ đệm hiệu quả"}, "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | description": {"message": "DOM lớn có thể làm các phép tính về kiểu và quy trình trình bày lại bố cục dài hơn, ảnh hưởng đến tốc độ phản hồi của trang. DOM lớn cũng sẽ làm tăng mức sử dụng bộ nhớ. [Tìm hiểu cách tránh kích thước DOM quá lớn](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | duration": {"message": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>"}, "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | element": {"message": "<PERSON><PERSON><PERSON> tử"}, "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | largeLayout": {"message": "<PERSON><PERSON> cục ({PH1} đ<PERSON><PERSON> t<PERSON>)"}, "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | largeStyleRecalc": {"message": "<PERSON><PERSON><PERSON> toán lại kiểu ({PH1} phần tử)"}, "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | maxChildren": {"message": "<PERSON><PERSON><PERSON> hết trẻ em"}, "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | maxDOMDepth": {"message": "Chiều sâu DOM"}, "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | statistic": {"message": "<PERSON><PERSON><PERSON><PERSON> kê"}, "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | title": {"message": "<PERSON><PERSON><PERSON>u hoá kích thước DOM"}, "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | topUpdatesDescription": {"message": "<PERSON><PERSON><PERSON> là những sự kiện tính toán lại bố cục và kiểu lớn nhất. Bạn có thể giảm tác động đến hiệu suất của các phần tử này bằng cách đơn giản hoá DOM."}, "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | totalElements": {"message": "<PERSON><PERSON>ng số phần tử"}, "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | value": {"message": "<PERSON><PERSON><PERSON> trị"}, "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | description": {"message": "<PERSON><PERSON><PERSON> cầu mạng đầu tiên là quan trọng nhất.  <PERSON><PERSON><PERSON><PERSON> độ trễ bằng cách tránh dùng đường liên kết chuyển hướng, đ<PERSON><PERSON> bả<PERSON> máy chủ phản hồi nhanh và bật tính năng nén văn bản."}, "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | failedRedirects": {"message": "<PERSON><PERSON> chuyển hướng ({PH1} chuyển hướng, +{PH2})"}, "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | failedServerResponseTime": {"message": "<PERSON><PERSON><PERSON> chủ phản hồi chậm (theo quan sát là {PH1})"}, "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | failedTextCompression": {"message": "Không áp dụng tính năng nén"}, "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | passingRedirects": {"message": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>"}, "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | passingServerResponseTime": {"message": "<PERSON><PERSON><PERSON> chủ phản hồ<PERSON> n<PERSON> (theo quan sát là {PH1})"}, "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | passingTextCompression": {"message": "<PERSON><PERSON> dụng t<PERSON>h năng nén văn bản"}, "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | redirectsLabel": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | serverResponseTimeLabel": {"message": "<PERSON>h<PERSON><PERSON> gian ph<PERSON>n hồi của máy chủ"}, "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | title": {"message": "<PERSON><PERSON> tr<PERSON> khi yêu cầu tài liệu"}, "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | uncompressedDownload": {"message": "<PERSON><PERSON><PERSON> x<PERSON>ng bản không n<PERSON>"}, "node_modules/@paulirish/trace_engine/models/trace/insights/DuplicatedJavaScript.js | columnDuplicatedBytes": {"message": "Số byte trùng lặp"}, "node_modules/@paulirish/trace_engine/models/trace/insights/DuplicatedJavaScript.js | columnSource": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "node_modules/@paulirish/trace_engine/models/trace/insights/DuplicatedJavaScript.js | description": {"message": "Xoá các mô-đun JavaScript trùng lặp lớn khỏi gói để giảm số byte mà hoạt động mạng tiêu thụ không cần thiết."}, "node_modules/@paulirish/trace_engine/models/trace/insights/DuplicatedJavaScript.js | title": {"message": "JavaScript trùng lặp"}, "node_modules/@paulirish/trace_engine/models/trace/insights/FontDisplay.js | description": {"message": "<PERSON><PERSON><PERSON> cân nhắc đặt [font-display](https://developer.chrome.com/blog/font-display) thành swap hoặc optional để đảm bảo văn bản hiển thị nhất quán. <PERSON><PERSON> thể tối ưu hoá swap hơn nữa để giảm thiểu thay đổi bố cục bằng thuộc tính [ghi đè chỉ số phông chữ](https://developer.chrome.com/blog/font-fallbacks)."}, "node_modules/@paulirish/trace_engine/models/trace/insights/FontDisplay.js | fontColumn": {"message": "Phông chữ"}, "node_modules/@paulirish/trace_engine/models/trace/insights/FontDisplay.js | title": {"message": "<PERSON><PERSON><PERSON> thị phông chữ"}, "node_modules/@paulirish/trace_engine/models/trace/insights/FontDisplay.js | wastedTimeColumn": {"message": "<PERSON>h<PERSON><PERSON> gian bị lãng phí"}, "node_modules/@paulirish/trace_engine/models/trace/insights/ForcedReflow.js | anonymous": {"message": "(<PERSON><PERSON> danh)"}, "node_modules/@paulirish/trace_engine/models/trace/insights/ForcedReflow.js | description": {"message": "Chế độ buộc chỉnh lại luồng diễn ra khi JavaScript truy vấn các thuộc tính hình học (chẳng hạn như offsetWidth) sau khi các kiểu đã bị vô hiệu hoá do trạng thái DOM có thay đổi. Điều này có thể dẫn đến hiệu suất kém. Tìm hiểu thêm về [chế độ buộc chỉnh lại luồng](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) và các biện pháp giảm thiểu có thể áp dụng."}, "node_modules/@paulirish/trace_engine/models/trace/insights/ForcedReflow.js | relatedStackTrace": {"message": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> ng<PERSON>n xếp"}, "node_modules/@paulirish/trace_engine/models/trace/insights/ForcedReflow.js | title": {"message": "Buộc chỉnh lại luồng"}, "node_modules/@paulirish/trace_engine/models/trace/insights/ForcedReflow.js | topTimeConsumingFunctionCall": {"message": "<PERSON><PERSON><PERSON> gọi hàm hàng đầu"}, "node_modules/@paulirish/trace_engine/models/trace/insights/ForcedReflow.js | totalReflowTime": {"message": "T<PERSON>ng thời gian chỉnh lại luồng"}, "node_modules/@paulirish/trace_engine/models/trace/insights/ForcedReflow.js | unattributed": {"message": "[ch<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> phân bổ]"}, "node_modules/@paulirish/trace_engine/models/trace/insights/INPBreakdown.js | description": {"message": "B<PERSON>t đầu kiểm tra phần phụ dài nhất. [<PERSON><PERSON> thể giảm thiểu sự chậm trễ](https://web.dev/articles/optimize-inp#optimize_interactions). <PERSON><PERSON> giảm thời gian xử lý, [hãy tối ưu hoá hiệu suất của luồng chính](https://web.dev/articles/optimize-long-tasks), thường là JS."}, "node_modules/@paulirish/trace_engine/models/trace/insights/INPBreakdown.js | duration": {"message": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>"}, "node_modules/@paulirish/trace_engine/models/trace/insights/INPBreakdown.js | inputDelay": {"message": "<PERSON><PERSON> tr<PERSON> khi nhập thông tin"}, "node_modules/@paulirish/trace_engine/models/trace/insights/INPBreakdown.js | noInteractions": {"message": "<PERSON><PERSON><PERSON><PERSON> phát hiện ra hoạt động tương tác nào"}, "node_modules/@paulirish/trace_engine/models/trace/insights/INPBreakdown.js | presentationDelay": {"message": "<PERSON><PERSON> tr<PERSON> khi trình bày"}, "node_modules/@paulirish/trace_engine/models/trace/insights/INPBreakdown.js | processingDuration": {"message": "<PERSON><PERSON><PERSON><PERSON> gian xử lý"}, "node_modules/@paulirish/trace_engine/models/trace/insights/INPBreakdown.js | subpart": {"message": "<PERSON>ầ<PERSON> phụ"}, "node_modules/@paulirish/trace_engine/models/trace/insights/INPBreakdown.js | title": {"message": "<PERSON><PERSON><PERSON> chi tiết về INP"}, "node_modules/@paulirish/trace_engine/models/trace/insights/ImageDelivery.js | description": {"message": "Vi<PERSON><PERSON> giảm thời gian tải hình ảnh xuống có thể cải thiện thời gian tải trang và LCP được cảm nhận. [Tìm hiểu thêm về cách tối ưu hoá kích thước hình <PERSON>nh](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "node_modules/@paulirish/trace_engine/models/trace/insights/ImageDelivery.js | estimatedSavings": {"message": "{PH1} (Ước tính là {PH2})"}, "node_modules/@paulirish/trace_engine/models/trace/insights/ImageDelivery.js | noOptimizableImages": {"message": "<PERSON><PERSON><PERSON><PERSON> có hình ảnh nào có thể tối ưu hoá"}, "node_modules/@paulirish/trace_engine/models/trace/insights/ImageDelivery.js | optimizeFile": {"message": "<PERSON><PERSON><PERSON>u ho<PERSON> kích thư<PERSON><PERSON> tệp"}, "node_modules/@paulirish/trace_engine/models/trace/insights/ImageDelivery.js | others": {"message": "{PH1} mục kh<PERSON>c"}, "node_modules/@paulirish/trace_engine/models/trace/insights/ImageDelivery.js | title": {"message": "<PERSON><PERSON><PERSON> thiện vi<PERSON><PERSON> phân phối hình <PERSON>nh"}, "node_modules/@paulirish/trace_engine/models/trace/insights/ImageDelivery.js | useCompression": {"message": "<PERSON><PERSON><PERSON><PERSON> tăng hệ số nén của hình ảnh có thể cải thiện kích thước tải xuống của hình ảnh này."}, "node_modules/@paulirish/trace_engine/models/trace/insights/ImageDelivery.js | useModernFormat": {"message": "Việc sử dụng định dạng hình ảnh hiện đại (WebP, AVIF) hoặc tăng độ nén của hình ảnh có thể cải thiện kích thước tải xuống của hình ảnh này."}, "node_modules/@paulirish/trace_engine/models/trace/insights/ImageDelivery.js | useResponsiveSize": {"message": "Tệ<PERSON> hình <PERSON>nh này lớn hơn mức cần thiết ({PH1}) so với kích thước hiển thị ({PH2}). H<PERSON>y sử dụng hình ảnh thích ứng để giảm kích thước tải xuống của hình <PERSON>nh."}, "node_modules/@paulirish/trace_engine/models/trace/insights/ImageDelivery.js | useVideoFormat": {"message": "Việc sử dụng định dạng video thay vì GIF có thể cải thiện kích thước tải xuống của nội dung động."}, "node_modules/@paulirish/trace_engine/models/trace/insights/LCPBreakdown.js | description": {"message": "Mỗi [phần phụ có một chiến lược cải thiện cụ thể](https://web.dev/articles/optimize-lcp#lcp-breakdown). Tốt nhất là thời gian hiển thị LCP (Nội dung lớn nhất hiển thị) nên được dùng để tải các tài nguyên thay vì bị chậm trễ."}, "node_modules/@paulirish/trace_engine/models/trace/insights/LCPBreakdown.js | duration": {"message": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>"}, "node_modules/@paulirish/trace_engine/models/trace/insights/LCPBreakdown.js | elementRenderDelay": {"message": "<PERSON>ộ tr<PERSON> khi hiển thị phần tử"}, "node_modules/@paulirish/trace_engine/models/trace/insights/LCPBreakdown.js | fieldDuration": {"message": "Trường p75"}, "node_modules/@paulirish/trace_engine/models/trace/insights/LCPBreakdown.js | noLcp": {"message": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON>t hiện ra LCP"}, "node_modules/@paulirish/trace_engine/models/trace/insights/LCPBreakdown.js | resourceLoadDelay": {"message": "<PERSON><PERSON> tr<PERSON> khi tải tài nguyên"}, "node_modules/@paulirish/trace_engine/models/trace/insights/LCPBreakdown.js | resourceLoadDuration": {"message": "<PERSON>h<PERSON><PERSON> lượng tải tài nguyên"}, "node_modules/@paulirish/trace_engine/models/trace/insights/LCPBreakdown.js | subpart": {"message": "<PERSON>ầ<PERSON> phụ"}, "node_modules/@paulirish/trace_engine/models/trace/insights/LCPBreakdown.js | timeToFirstByte": {"message": "Th<PERSON><PERSON> gian cho byte đầu tiên"}, "node_modules/@paulirish/trace_engine/models/trace/insights/LCPBreakdown.js | title": {"message": "<PERSON><PERSON><PERSON> chi tiết về LCP (Nội dung lớn nhất hiển thị)"}, "node_modules/@paulirish/trace_engine/models/trace/insights/LCPDiscovery.js | description": {"message": "Tối ưu hoá LCP (Nội dung lớn nhất hiển thị) bằng cách giúp trình duyệt [có thể tìm thấy](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) hình ảnh LCP dựa vào HTML ngay lập tức và [tránh tải từng phần](https://web.dev/articles/lcp-lazy-loading)"}, "node_modules/@paulirish/trace_engine/models/trace/insights/LCPDiscovery.js | fetchPriorityApplied": {"message": "<PERSON><PERSON> áp dụng giá trị \"high\" củ<PERSON> thu<PERSON><PERSON> t<PERSON>h fetchpriority"}, "node_modules/@paulirish/trace_engine/models/trace/insights/LCPDiscovery.js | fetchPriorityShouldBeApplied": {"message": "Bạn nên áp dụng fetchpriority=high"}, "node_modules/@paulirish/trace_engine/models/trace/insights/LCPDiscovery.js | lazyLoadNotApplied": {"message": "ch<PERSON><PERSON> áp dụng phư<PERSON>ng thức tải từng phần"}, "node_modules/@paulirish/trace_engine/models/trace/insights/LCPDiscovery.js | lcpLoadDelay": {"message": "<PERSON><PERSON><PERSON>nh LCP được tải sau {PH1} kể từ thời điểm bắt đầu sớm nhất."}, "node_modules/@paulirish/trace_engine/models/trace/insights/LCPDiscovery.js | noLcp": {"message": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON>t hiện ra LCP"}, "node_modules/@paulirish/trace_engine/models/trace/insights/LCPDiscovery.js | noLcpResource": {"message": "Không phát hiện ra tài nguyên LCP vì LCP không phải là hình <PERSON>nh"}, "node_modules/@paulirish/trace_engine/models/trace/insights/LCPDiscovery.js | requestDiscoverable": {"message": "<PERSON><PERSON> thể tìm thấy yêu cầu trong tài liệu ban đầu"}, "node_modules/@paulirish/trace_engine/models/trace/insights/LCPDiscovery.js | title": {"message": "<PERSON><PERSON><PERSON> yêu c<PERSON>u <PERSON>"}, "node_modules/@paulirish/trace_engine/models/trace/insights/LegacyJavaScript.js | columnScript": {"message": "<PERSON><PERSON><PERSON>"}, "node_modules/@paulirish/trace_engine/models/trace/insights/LegacyJavaScript.js | columnWastedBytes": {"message": "Số byte bị lãng phí"}, "node_modules/@paulirish/trace_engine/models/trace/insights/LegacyJavaScript.js | description": {"message": "Mã Polyfill và Transform cho phép các trình duyệt cũ sử dụng những tính năng mới của JavaScript. <PERSON><PERSON>, c<PERSON> nhiều tính năng không cần thiết cho trình duyệt hiện đại. Hãy cân nhắc sửa đổi quy trình xây dựng JavaScript để không chuyển đổi mã nguồn các tính năng [Đường cơ sở](https://web.dev/articles/baseline-and-polyfills), trừ phi bạn biết rằng mình phải hỗ trợ các trình duyệt cũ. [Tìm hiểu lý do hầu hết các trang web có thể triển khai mã ES6+ mà không cần chuyển đổi mã nguồn](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "node_modules/@paulirish/trace_engine/models/trace/insights/LegacyJavaScript.js | title": {"message": "JavaScript cũ"}, "node_modules/@paulirish/trace_engine/models/trace/insights/ModernHTTP.js | description": {"message": "HTTP/2 và HTTP/3 mang lại nhiều lợi ích hơn HTTP/1.1, chẳng hạn như ghép kênh. [Tìm hiểu thêm về cách sử dụng HTTP hiện đại](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "node_modules/@paulirish/trace_engine/models/trace/insights/ModernHTTP.js | noOldProtocolRequests": {"message": "<PERSON>hông có yêu cầu nào sử dụng HTTP/1.1 hoặc việc sử dụng HTTP/1.1 hiện tại không mang lại cơ hội tối ưu hoá đáng kể. Yêu cầu HTTP/1.1 chỉ được gắn cờ nếu có từ 6 tài sản tĩnh cùng nguồn gốc trở lên và không được phân phát từ môi trường phát triển cục bộ hoặc nguồn của bên thứ ba."}, "node_modules/@paulirish/trace_engine/models/trace/insights/ModernHTTP.js | protocol": {"message": "<PERSON><PERSON><PERSON> th<PERSON>"}, "node_modules/@paulirish/trace_engine/models/trace/insights/ModernHTTP.js | request": {"message": "<PERSON><PERSON><PERSON> c<PERSON>"}, "node_modules/@paulirish/trace_engine/models/trace/insights/ModernHTTP.js | title": {"message": "HTTP hiện đại"}, "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | columnOrigin": {"message": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>c"}, "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | columnRequest": {"message": "<PERSON><PERSON><PERSON> c<PERSON>"}, "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | columnSource": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | columnTime": {"message": "<PERSON><PERSON><PERSON><PERSON> gian"}, "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | columnWastedMs": {"message": "<PERSON><PERSON><PERSON> ti<PERSON><PERSON> kiệm LCP ước t<PERSON>h"}, "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | crossoriginWarning": {"message": "<PERSON><PERSON><PERSON> n<PERSON>i trướ<PERSON> chưa sử dụng. <PERSON><PERSON><PERSON> kiểm tra để đảm b<PERSON><PERSON> thu<PERSON><PERSON> t<PERSON>h crossorigin được sử dụng đúng cách."}, "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | description": {"message": "[Tr<PERSON><PERSON> tạo chuỗi các yêu cầu quan trọng](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains) bằng cách giảm độ dài chuỗi, gi<PERSON>m kích thước tài nguyên tải xuống hoặc trì hoãn tải xuống các tài nguyên không cần thiết để cải thiện tốc độ tải trang."}, "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | estSavingTableDescription": {"message": "Thêm gợi ý [kết nối trước](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) cho các nguồn gốc quan trọng nhất, nh<PERSON><PERSON> hãy cố gắng sử dụng không quá 4 gợi ý."}, "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | estSavingTableTitle": {"message": "<PERSON><PERSON> xuất kết nối trước"}, "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | maxCriticalPathLatency": {"message": "<PERSON><PERSON> trễ tối đa của đường dẫn quan trọng:"}, "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | noNetworkDependencyTree": {"message": "<PERSON>h<PERSON>ng có tác vụ kết xuất nào chịu ảnh hưởng của phần phụ thuộc mạng"}, "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | noPreconnectCandidates": {"message": "<PERSON><PERSON><PERSON><PERSON> có điểm gốc nào khác phù hợp để kết nối trước"}, "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | noPreconnectOrigins": {"message": "kh<PERSON><PERSON> có điểm gốc nào đư<PERSON><PERSON> kết nối trước"}, "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | preconnectOriginsTableDescription": {"message": "Gợi ý [kết nối trước](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) giúp trình duyệt thiết lập kết nối sớm hơn trong quá trình tải trang, nhờ đó tiết kiệm thời gian khi gửi yêu cầu đầu tiên đối với điểm gốc đó. Sau đây là những điểm gốc mà trang đã kết nối trước."}, "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | preconnectOriginsTableTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> gốc đ<PERSON><PERSON><PERSON> kết nối trước"}, "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | title": {"message": "<PERSON><PERSON><PERSON> ph<PERSON>n phụ thuộc mạng"}, "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | tooManyPreconnectLinksWarning": {"message": "<PERSON><PERSON> phát hiện ra hơn 4 đường kết nối preconnect. Bạn nên dùng những đường kết nối này một cách hợp lý và chỉ dùng cho các điểm gốc quan trọng nhất."}, "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | unusedWarning": {"message": "<PERSON><PERSON><PERSON> nối trước chưa sử dụng. Chỉ sử dụng preconnect cho những điểm gốc mà trang có khả năng yêu cầu."}, "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | warningDescription": {"message": "<PERSON>r<PERSON><PERSON> tạo chuỗi các yêu cầu quan trọng bằng cách giảm độ dài chuỗi, gi<PERSON><PERSON> kích thước tài nguyên tải xuống hoặc trì hoãn tải xuống các tài nguyên không cần thiết để cải thiện tốc độ tải trang."}, "node_modules/@paulirish/trace_engine/models/trace/insights/RenderBlocking.js | description": {"message": "<PERSON><PERSON><PERSON> yêu cầu đang chặn quá trình kết xuất ban đầu của trang, điều này có thể làm trễ LCP. [Việc hoãn hoặc dùng cùng dòng](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) có thể di chuyển các yêu cầu mạng này ra khỏi đường dẫn quan trọng."}, "node_modules/@paulirish/trace_engine/models/trace/insights/RenderBlocking.js | duration": {"message": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>"}, "node_modules/@paulirish/trace_engine/models/trace/insights/RenderBlocking.js | noRenderBlocking": {"message": "<PERSON><PERSON><PERSON><PERSON> có yêu cầu chặn hiển thị cho thao tác điều hướng này"}, "node_modules/@paulirish/trace_engine/models/trace/insights/RenderBlocking.js | renderBlockingRequest": {"message": "<PERSON><PERSON><PERSON> c<PERSON>"}, "node_modules/@paulirish/trace_engine/models/trace/insights/RenderBlocking.js | title": {"message": "<PERSON><PERSON><PERSON> cầu chặn quá trình hiển thị"}, "node_modules/@paulirish/trace_engine/models/trace/insights/SlowCSSSelector.js | description": {"message": "Nếu thấy chi phí của T<PERSON>h toán lại kiểu vẫn cao, bạn có thể tối ưu hoá bộ chọn để giảm chi phí. [Tối ưu hoá bộ chọn](https://developer.chrome.com/docs/devtools/performance/selector-stats) có tỷ lệ phần trăm đường dẫn chậm và thời gian trôi qua đều cao. Bộ chọn đơn giản hơn, số lượng bộ chọn ít hơn, DOM nhỏ hơn và DOM nông hơn sẽ giúp giảm chi phí khớp."}, "node_modules/@paulirish/trace_engine/models/trace/insights/SlowCSSSelector.js | elapsed": {"message": "Thời gian đã trôi qua"}, "node_modules/@paulirish/trace_engine/models/trace/insights/SlowCSSSelector.js | enableSelectorData": {"message": "<PERSON><PERSON><PERSON><PERSON> tìm thấy dữ liệu về bộ chọn CSS. Bạn cần bật số liệu thống kê về bộ chọn CSS trong phần cài đặt bảng điều khiển hiệu suất."}, "node_modules/@paulirish/trace_engine/models/trace/insights/SlowCSSSelector.js | matchAttempts": {"message": "Số lần kh<PERSON>p"}, "node_modules/@paulirish/trace_engine/models/trace/insights/SlowCSSSelector.js | matchCount": {"message": "Số lượng trùng khớp"}, "node_modules/@paulirish/trace_engine/models/trace/insights/SlowCSSSelector.js | title": {"message": "<PERSON><PERSON><PERSON> <PERSON><PERSON> c<PERSON><PERSON> chọn CSS"}, "node_modules/@paulirish/trace_engine/models/trace/insights/SlowCSSSelector.js | topSelectorElapsedTime": {"message": "Thời gian đã trôi qua của bộ chọn hàng đầu"}, "node_modules/@paulirish/trace_engine/models/trace/insights/SlowCSSSelector.js | topSelectorMatchAttempt": {"message": "Nỗ lực khớp bộ chọn hàng đầu"}, "node_modules/@paulirish/trace_engine/models/trace/insights/SlowCSSSelector.js | topSelectors": {"message": "<PERSON><PERSON> chọn hàng đầu"}, "node_modules/@paulirish/trace_engine/models/trace/insights/SlowCSSSelector.js | total": {"message": "Tổng"}, "node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | columnMainThreadTime": {"message": "<PERSON>h<PERSON>i gian thực thi trên chuỗi chính"}, "node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | columnThirdParty": {"message": "<PERSON><PERSON><PERSON>ứ ba"}, "node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | columnTransferSize": {"message": "<PERSON><PERSON><PERSON> c<PERSON><PERSON>"}, "node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | description": {"message": "Mã của bên thứ ba có thể tác động đáng kể đến hiệu suất tải. [Giảm và trì hoãn việc tải mã của bên thứ ba](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/) để ưu tiên nội dung trên trang của bạn."}, "node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | noThirdParties": {"message": "<PERSON><PERSON><PERSON><PERSON> tìm thấy bên thứ ba nào"}, "node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | title": {"message": "bên thứ ba"}, "node_modules/@paulirish/trace_engine/models/trace/insights/Viewport.js | description": {"message": "<PERSON><PERSON>t động tương tác nhấn có thể [bị chậm tới 300 mili giây](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) nếu khung nhìn chưa được tối ưu hoá cho thiết bị di động."}, "node_modules/@paulirish/trace_engine/models/trace/insights/Viewport.js | mobileTapDelayLabel": {"message": "<PERSON><PERSON> trễ khi nhấn trên thiết bị di động"}, "node_modules/@paulirish/trace_engine/models/trace/insights/Viewport.js | title": {"message": "<PERSON><PERSON><PERSON>u hoá khung nhìn cho thiết bị di động"}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | HTTPMethodNotGET": {"message": "Chỉ những trang được tải qua yêu cầu GET mới đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | HTTPStatusNotOK": {"message": "Chỉ những trang có mã trạng thái là 2XX mới có thể lưu vào bộ nhớ đệm."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | JavaScriptExecution": {"message": "Chrome đã phát hiện thấy một lần thử thực thi JavaScript khi đang ở bộ nhớ đệm."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | appBanner": {"message": "<PERSON>hững trang đã yêu cầu AppBanner hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | backForwardCacheDisabled": {"message": "Bộ nhớ đệm cho thao tác tiến/lùi đã bị tắt do cờ. H<PERSON>y truy cập chrome://flags/#back-forward-cache để bật tính năng đó trên thiết bị này."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | backForwardCacheDisabledByCommandLine": {"message": "Bộ nhớ đệm cho thao tác tiến/lùi đã bị tắt do dòng lệnh."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | backForwardCacheDisabledByLowMemory": {"message": "Bộ nhớ đệm cho thao tác tiến/lùi đã bị tắt do thiếu bộ nhớ."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | backForwardCacheDisabledForDelegate": {"message": "<PERSON><PERSON> được uỷ quyền không hỗ trợ bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | backForwardCacheDisabledForPrerender": {"message": "Bộ nhớ đệm cho thao tác tiến/lùi đã bị tắt đối với trình kết xuất trước."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | broadcastChannel": {"message": "<PERSON>h<PERSON>ng thể lưu trang này vào bộ nhớ đệm vì trang có một thực thể BroadcastChannel chứa các trình nghe đã đăng ký."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | cacheControlNoStore": {"message": "<PERSON><PERSON><PERSON><PERSON> trang có tiêu đề cache-control:no-store không thể chuyển sang bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | cacheFlushed": {"message": "Bộ nhớ đệm này đã bị xoá một cách có chủ đích."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | cacheLimit": {"message": "Trang này đã bị loại khỏi bộ nhớ đệm để cho phép lưu một trang khác vào bộ nhớ đệm."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | containsPlugins": {"message": "<PERSON>h<PERSON>ng trang có chứa trình bổ trợ hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | contentDiscarded": {"message": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>"}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | contentFileChooser": {"message": "Những trang sử dụng API FileChooser không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | contentFileSystemAccess": {"message": "Những trang sử dụng API Truy cập hệ thống tệp không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | contentMediaDevicesDispatcherHost": {"message": "Những trang sử dụng Trình điều phối thiết bị truyền thông không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | contentMediaPlay": {"message": "<PERSON><PERSON>t trình phát đa phương tiện đang phát khi rời khỏi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | contentMediaSession": {"message": "Những trang sử dụng API MediaSession và đặt một trạng thái phát không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | contentMediaSessionService": {"message": "Những trang sử dụng API MediaSession và đặt các trình xử lý hành động hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | contentScreenReader": {"message": "Bộ nhớ đệm cho thao tác tiến/lùi bị tắt do trình đọc màn hình."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | contentSecurityHandler": {"message": "Những trang sử dụng SecurityHandler không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | contentSerial": {"message": "Những trang sử dụng API nối tiếp không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | contentWebAuthenticationAPI": {"message": "Những trang sử dụng API WebAuthetication không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | contentWebBluetooth": {"message": "Những trang sử dụng API WebBluetooth không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | contentWebUSB": {"message": "Những trang sử dụng API WebUSB không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | cookieDisabled": {"message": "Bộ nhớ đệm cho thao tác tiến/lùi bị tắt do các cookie bị tắt trên một trang sử dụng Cache-Control: no-store."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | dedicatedWorkerOrWorklet": {"message": "Những trang sử dụng một trình xử lý hoặc worklet riêng hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | documentLoaded": {"message": "<PERSON><PERSON><PERSON> liệu này chưa tải xong trước khi rời khỏi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | embedderAppBannerManager": {"message": "App Banner đang xuất hiện trong khi rời đi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "<PERSON><PERSON><PERSON><PERSON> quản lý mật khẩu của Chrome đang xuất hiện trong khi rời đi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "Quy trình lọc DOM đang diễn ra trong khi rời đi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | embedderDomDistillerViewerSource": {"message": "DOM Distiller Viewer đ<PERSON> xuất hiện trong khi rời đi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | embedderExtensionMessaging": {"message": "Bộ nhớ đệm cho thao tác tiến/lùi bị tắt do các tiện ích sử dụng API nhắn tin."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | embedderExtensionMessagingForOpenPort": {"message": "<PERSON><PERSON><PERSON> tiện ích có kết nối liên tục phải ngắt kết nối trước khi chuyển sang bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | embedderExtensionSentMessageToCachedFrame": {"message": "<PERSON><PERSON><PERSON> tiện ích có kết nối liên tục đã cố gắng gửi thông báo đến các khung trong bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | embedderExtensions": {"message": "Bộ nhớ đệm cho thao tác tiến/lùi bị tắt do các tiện ích."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | embedderModalDialog": {"message": "<PERSON>ộ<PERSON> thoại phư<PERSON> thức (chẳng hạn như gửi lại biểu mẫu) hoặc hộp thoại mật khẩu http đang xuất hiện trên trang trong khi rời đi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | embedderOfflinePage": {"message": "Trang ngoại tuyến đang xuất hiện trong khi rời đi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | embedderOomInterventionTabHelper": {"message": "Thanh Out-Of-Memory Intervention đang xuất hiện trong khi rời đi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | embedderPermissionRequestManager": {"message": "<PERSON><PERSON> yêu cầu câ<PERSON>p quyền trong khi rời đi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | embedderPopupBlockerTabHelper": {"message": "<PERSON>r<PERSON><PERSON> chặn cửa sổ bật lên đang xuất hiện trong khi rời đi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | embedderSafeBrowsingThreatDetails": {"message": "Thông tin chi tiết về công cụ Duyệt web an toàn đang xuất hiện trong khi rời đi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "<PERSON>ông cụ Duyệt web an toàn coi trang này là một cửa sổ bật lên sai mục đích và bị chặn."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "<PERSON>ột trình chạy dịch vụ đã đư<PERSON><PERSON> kích hoạt khi trang này đang ở bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | errorDocument": {"message": "<PERSON><PERSON> tắt bộ nhớ đệm cho thao tác tiến/lùi do lỗi tài liệu"}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | fencedFramesEmbedder": {"message": "<PERSON><PERSON><PERSON><PERSON> lưu trữ được trang dùng FencedFrames vào bfcache."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | foregroundCacheLimit": {"message": "Trang này đã bị loại khỏi bộ nhớ đệm để cho phép lưu một trang khác vào bộ nhớ đệm."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | grantedMediaStreamAccess": {"message": "<PERSON><PERSON><PERSON>ng trang đã cấp quyền truy cập luồng đa phương tiện hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | haveInnerContents": {"message": "<PERSON><PERSON><PERSON><PERSON> trang có một số loại nội dung đư<PERSON><PERSON> nhúng nhất định (ví dụ: PDF) hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | idleManager": {"message": "<PERSON>hững trang sử dụng IdleManager hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | indexedDBConnection": {"message": "Những trang có kết nối IndexedDB mở hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | indexedDBEvent": {"message": "Bộ nhớ đệm cho thao tác tiến/lùi bị tắt do sự kiện IndexedDB."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | ineligibleAPI": {"message": "<PERSON><PERSON><PERSON> <PERSON> được sử dụng không đủ điều kiện."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | injectedJavascript": {"message": "Những trang có JavaScript được chèn vào bằng tiện ích hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | injectedStyleSheet": {"message": "Những trang có StyleSheet được chèn vào bằng tiện ích hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | internalError": {"message": "Lỗi nội bộ."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "Bộ nhớ đệm cho thao tác tiến/lùi bị tắt do một số yêu cầu mạng JavaScript nhận được tài nguyên có tiêu đề Cache-Control: no-store."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | keepaliveRequest": {"message": "Bộ nhớ đệm cho thao tác tiến/lùi đã bị tắt do có yêu cầu duy trì hoạt động."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | keyboardLock": {"message": "Những trang sử dụng tính năng <PERSON>a bàn phím hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | loading": {"message": "<PERSON>rang này chưa tải xong trước khi rời khỏi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | mainResourceHasCacheControlNoCache": {"message": "<PERSON>h<PERSON>ng trang có tài nguyên ch<PERSON>h chứa cache-control:no-cache không thể chuyển sang bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | mainResourceHasCacheControlNoStore": {"message": "<PERSON>h<PERSON>ng trang có tài nguyên ch<PERSON>h chứa cache-control:no-store không thể chuyển sang bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | navigationCancelledWhileRestoring": {"message": "Ho<PERSON>t động điều hướng đã bị huỷ trước khi có thể khôi phục trang từ bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | networkExceedsBufferLimit": {"message": "Trang này đã bị loại khỏi bộ nhớ đệm vì một kết nối mạng đang hoạt động đã nhận quá nhiều dữ liệu. Chrome giới hạn lượng dữ liệu mà một trang có thể nhận trong khi được lưu vào bộ nhớ đệm."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "<PERSON>h<PERSON>ng trang có tính năng tìm nạp() hoặc XHR đang tiến hành hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | networkRequestRedirected": {"message": "Trang này đã bị loại khỏi bộ nhớ đệm cho thao tác tiến/lùi vì một yêu cầu mạng đang hoạt động đã dẫn đến lệnh chuyển hướng."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | networkRequestTimeout": {"message": "Trang này đã bị loại khỏi bộ nhớ đệm vì một kết nối mạng ở trạng thái mở quá lâu. Chrome giới hạn khoảng thời gian mà một trang có thể nhận dữ liệu trong khi được lưu vào bộ nhớ đệm."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | noResponseHead": {"message": "<PERSON><PERSON><PERSON><PERSON> trang không có tiêu đề phản hồi hợp lệ không thể chuyển sang bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | notMainFrame": {"message": "<PERSON><PERSON>t động điều hướng đã diễn ra trong một khung không phải khung ch<PERSON>h."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | outstandingIndexedDBTransaction": {"message": "Trang có các giao dịch đang diễn ra trên cơ sở dữ liệu đã lập chỉ mục hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | outstandingNetworkRequestDirectSocket": {"message": "<PERSON>h<PERSON>ng trang có một yêu cầu mạng đang tiến hành hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | outstandingNetworkRequestFetch": {"message": "<PERSON><PERSON><PERSON><PERSON> trang có một yêu cầu mạng tìm nạp đang tiến hành hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | outstandingNetworkRequestOthers": {"message": "<PERSON>h<PERSON>ng trang có một yêu cầu mạng đang tiến hành hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | outstandingNetworkRequestXHR": {"message": "<PERSON>hững trang có một yêu cầu mạng XHR đang tiến hành hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | paymentManager": {"message": "Những trang sử dụng PaymentManager hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | pictureInPicture": {"message": "Những trang sử dụng tính năng Hình trong hình hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | printing": {"message": "<PERSON>hững trang hiển thị Giao diện người dùng cho thao tác in hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | relatedActiveContentsExist": {"message": "Trang này đã được mở bằng \"window.open()\" và có một thẻ khác tham chiếu đến thành phần đó, hoặc trang này đã mở một cửa sổ."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | rendererProcessCrashed": {"message": "<PERSON><PERSON> trình kết xuất đồ hoạ cho trang trong bộ nhớ đệm cho thao tác tiến/lùi đã gặp sự cố."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | rendererProcessKilled": {"message": "<PERSON><PERSON> trình kết xuất đồ hoạ cho trang trong bộ nhớ đệm cho thao tác tiến/lùi đã bị tắt."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | requestedAudioCapturePermission": {"message": "<PERSON>hững trang đã yêu cầu quyền ghi âm hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | requestedBackForwardCacheBlockedSensors": {"message": "<PERSON>h<PERSON>ng trang đã yêu cầu quyền truy cập cảm biến hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | requestedBackgroundWorkPermission": {"message": "Những trang đã yêu cầu quyền tìm nạp hoặc đồng bộ hoá dưới nền hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | requestedMIDIPermission": {"message": "<PERSON>h<PERSON>ng trang đã yêu cầu quyền MIDI hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | requestedNotificationsPermission": {"message": "<PERSON>hững trang đã yêu cầu quyền thông báo hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | requestedStorageAccessGrant": {"message": "<PERSON>h<PERSON>ng trang đã yêu cầu quyền truy cập bộ nhớ hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | requestedVideoCapturePermission": {"message": "<PERSON>h<PERSON>ng trang đã yêu cầu quyền quay video hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | schemeNotHTTPOrHTTPS": {"message": "Chỉ những trang có lược đồ URL là HTTP/HTTPS mới có thể lưu vào bộ nhớ đệm."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | serviceWorkerClaim": {"message": "Trang này đã được một trình chạy dịch vụ xác nhận khi đang ở bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | serviceWorkerPostMessage": {"message": "Một trình chạy dịch vụ đã cố gửi một MessageEvent cho trang trong bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | serviceWorkerUnregistration": {"message": "ServiceWorker đã bị huỷ đăng ký khi một trang đang ở bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | serviceWorkerVersionActivation": {"message": "Trang này đã bị loại khỏi bộ nhớ đệm cho thao tác tiến/lùi do việc kích hoạt một trình chạy dịch vụ."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | sessionRestored": {"message": "Chrome đã khởi động lại và xoá các mục trong bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | sharedWorker": {"message": "Những trang sử dụng SharedWorker hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | sharedWorkerMessage": {"message": "Trang này đã bị xoá khỏi bộ nhớ đệm vì nhận được thông báo từ một SharedWorker"}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | speechRecognizer": {"message": "Những trang sử dụng SpeechRecognizer hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | speechSynthesis": {"message": "Những trang sử dụng SpeechSynthesis hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | subframeIsNavigating": {"message": "Một iframe trên trang này đã bắt đầu một hoạt động điều hướng chưa hoàn tất."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | subresourceHasCacheControlNoCache": {"message": "<PERSON>h<PERSON>ng trang có tài nguyên phụ chứa cache-control:no-cache không thể chuyển sang bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | subresourceHasCacheControlNoStore": {"message": "<PERSON>h<PERSON>ng trang có tài nguyên phụ chứa cache-control:no-store không thể chuyển sang bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | timeout": {"message": "Trang này đã vượt quá thời gian tối đa trong bộ nhớ đệm cho thao tác tiến/lùi và đã hết hạn."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | timeoutPuttingInCache": {"message": "Trang này đã hết thời gian chờ chuyển sang bộ nhớ đệm cho thao tác tiến/lùi (kh<PERSON> năng là do các trình xử lý ẩn trang chạy trong thời gian dài)."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | unloadHandlerExistsInMainFrame": {"message": "Trang này có một trình xử lý huỷ tải trong khung ch<PERSON>h."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | unloadHandlerExistsInSubFrame": {"message": "Trang này có một trình xử lý huỷ tải trong khung phụ."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | userAgentOverrideDiffers": {"message": "Tr<PERSON><PERSON> duyệt đã thay đổi tiêu đề ghi đè tác nhân người dùng."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | wasGrantedMediaAccess": {"message": "<PERSON>hững trang đã cấp quyền quay video hoặc ghi âm hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | webDatabase": {"message": "Những trang sử dụng WebDatabase hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | webHID": {"message": "Những trang sử dụng WebHID hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | webLocks": {"message": "Những trang sử dụng WebLocks hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | webNfc": {"message": "Những trang sử dụng WebNfc hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | webOTPService": {"message": "<PERSON>h<PERSON>ng trang sử dụng WebOTPService hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | webRTC": {"message": "Những trang có WebRTC không thể chuyển sang bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | webRTCSticky": {"message": "Bộ nhớ đệm cho thao tác tiến/lùi bị tắt vì WebRTC đã được dùng."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | webShare": {"message": "Những trang sử dụng WebShare hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | webSocket": {"message": "Những trang có WebSocket không thể chuyển sang bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | webSocketSticky": {"message": "Bộ nhớ đệm cho thao tác tiến/lùi bị tắt vì WebSocket đã đư<PERSON><PERSON> dùng."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | webTransport": {"message": "Những trang có WebTransport không thể chuyển sang bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | webTransportSticky": {"message": "Bộ nhớ đệm cho thao tác tiến/lùi bị tắt vì WebTransport đã đư<PERSON>c dùng."}, "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | webXR": {"message": "Những trang sử dụng WebXR hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "<PERSON><PERSON><PERSON> với nội dung động, h<PERSON>y sử dụng [`amp-anim`](https://amp.dev/documentation/components/amp-anim/) để gi<PERSON>m thiểu mức sử dụng CPU khi nội dung nằm ngoài màn hình."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "<PERSON><PERSON> nhắc hiển thị tất cả các thành phần [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) của bạn ở định dạng WebP, đồng thời chỉ định phương án dự phòng thích hợp cho các trình duyệt khác. [Tìm hiểu thêm](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "<PERSON><PERSON><PERSON> bảo rằng bạn đang sử dụng [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) cho quá trình tự động tải từng phần đối với hình ảnh. [Tìm hiểu thêm](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "<PERSON><PERSON> dụng các công cụ như [<PERSON>r<PERSON><PERSON> tối ưu hóa AMP](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer) đ<PERSON> [hiển thị các bố cục AMP ở phía máy chủ](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "<PERSON><PERSON> khảo [tài liệu về AMP](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/) để đảm bảo tất cả các kiểu đều được hỗ trợ."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "Thành phần [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) hỗ trợ thuộc tính [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/) để chỉ định thành phần hình ảnh sẽ sử dụng dựa trên kích thước màn hình. [Tìm hiểu thêm](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "<PERSON><PERSON> nhắc cuộn ảo bằng Component Dev Kit (CDK) nếu đang hiển thị danh sách rất lớn. [Tìm hiểu thêm](https://web.dev/virtualize-lists-with-angular-cdk/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "<PERSON><PERSON> dụng tùy chọn [phân tách mã cấp định tuyến](https://web.dev/route-level-code-splitting-in-angular/) để giảm thiểu kích thước của các gói JavaScript. <PERSON><PERSON><PERSON> thời, cân nhắc lưu trướ<PERSON> vào bộ nhớ đệm các tài sản có [trình chạy dịch v<PERSON>](https://web.dev/precaching-with-the-angular-service-worker/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "<PERSON><PERSON>u bạn đang dùng giao diện dòng lệnh (CLI) <PERSON><PERSON>, hãy đảm bảo các bản dựng được tạo ở chế độ sản xuất. [Tìm hiểu thêm](https://angular.io/guide/deployment#enable-runtime-production-mode)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "N<PERSON>u bạn đang dùng giao diện dòng lệnh (CLI) <PERSON><PERSON>, hãy đưa các sơ đồ nguồn vào bản phát hành chính thức để kiểm tra các gói của bạn. [Tìm hiểu thêm](https://angular.io/guide/deployment#inspect-the-bundles)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "<PERSON><PERSON><PERSON> trư<PERSON><PERSON> các mục định tuyến để di chuyển nhanh hơn. [Tìm hiểu thêm](https://web.dev/route-preloading-in-angular/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "<PERSON><PERSON> nhắc dùng tiện ích `BreakpointObserver` trong Component Dev Kit (CDK) để quản lý các điểm ngắt hình ảnh. [Tìm hiểu thêm](https://material.angular.io/cdk/layout/overview)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "<PERSON><PERSON><PERSON> cân nhắc tải `GIF` lên một dịch vụ để có thể nhúng hình ảnh này ở dạng video HTML5."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "<PERSON><PERSON> nhắc định cấu hình [định dạng hình ảnh WebP với kiểu Chuyển đổi hình ảnh](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles) trên trang web."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "<PERSON><PERSON><PERSON> cân nhắc định cấu hình tính năng tải hình ảnh từng phần trong `Dr<PERSON>al`. Trình định dạng trường cho hình ảnh hỗ trợ `lazy` hoặc `eager`."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | prioritize-lcp-image": {"message": "<PERSON><PERSON><PERSON> phần tử LCP được thêm vào trang qua phương thức động, bạn nên tối ưu hoá hình ảnh để cải thiện LCP. [Tìm hiểu thêm](https://www.smashingmagazine.com/2023/08/methods-improving-drupal-largest-contentful-paint-core-web-vital/)"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | redirects": {"message": "<PERSON><PERSON><PERSON> lần chuyển hướng sẽ khiến tốc độ tải trang chậm thêm. Nếu bạn đã cài đặt mô-đun [Chuyển hướng](https://www.drupal.org/project/redirect), hãy xem xét xem có thể loại bỏ các lượt chuyển hướng không cần thiết hay không. [Tìm hiểu thêm](https://developers.google.com/web/tools/lighthouse/audits/redirects)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "<PERSON><PERSON><PERSON> cân nhắc sử dụng [một mô-đun](https://www.drupal.org/project/critical_css) để chèn CSS và JavaScript quan trọng vào cùng dòng, đồng thời sử dụng thuộc tính trì hoãn cho CSS hoặc JavaScript không quan trọng."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "Gi<PERSON>m tải lưu lượng truy cập bằng một hoặc nhiều mô-đun lưu vào bộ nhớ đệm `<PERSON><PERSON><PERSON>` như `Internal Page Cache`, `Internal Dynamic Page Cache` và `BigPipe`. H<PERSON>y kết hợp các mô-đun này với CDN để cải thiện thêm thời gian phản hồi. Máy chủ lưu trữ cần tận dụng PHP OPcache. Hãy cân nhắc dùng khả năng lưu vào bộ nhớ đệm của bộ nhớ, như lưu và<PERSON> hoặc Mem<PERSON>, để giảm số lần truy vấn cơ sở dữ liệu. <PERSON><PERSON><PERSON><PERSON> cù<PERSON>, hãy sử dụng giao diện, mô-đun và máy chủ nhanh hơn, có hiệu suất cao để giảm thời gian phản hồi của máy chủ."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "Hãy cân nhắc sử dụng [<PERSON><PERSON><PERSON> hình ảnh thích ứng](https://www.drupal.org/documentation/modules/responsive_image) để giảm kích thước của hình ảnh tải trên trang. Nếu bạn đang dùng `Views` để hiển thị nhiều mục nội dung trên một trang, hãy cân nhắc triển khai quá trình phân trang để giới hạn số mục nội dung hiển thị trên trang cụ thể."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "<PERSON><PERSON><PERSON> bảo bạn đã bật lựa chọn \"Aggregate CSS files\" (Tổng hợp các tệp CSS) trên trang \"Administration » Configuration » Development\" (Quản trị » <PERSON><PERSON><PERSON> hình » Phát triển)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "<PERSON><PERSON><PERSON> bảo bạn đã bật lựa chọn \"Aggregate JavaScript files\" (Tổng hợp các tệp JavaScript) trên trang \"Administration » Configuration » Development\" (Quản trị » <PERSON><PERSON><PERSON> hình » Phát triển)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "Hãy cân nhắc xoá những quy tắc CSS không dùng đến và chỉ đính kèm các thư viện `<PERSON><PERSON><PERSON>` cần thiết vào trang hoặc thành phần liên quan trên trang. Vui lòng xem [tài liệu`<PERSON><PERSON><PERSON>`](https://www.drupal.org/docs/develop/theming-drupal/adding-assets-css-js-to-a-drupal-theme-via-librariesyml#define) này để biết thông tin chi tiết. Để xác định các thư viện đã đính kèm đang thêm CSS không liên quan, hãy thử chạy [mức độ sử dụng mã](https://developer.chrome.com/docs/devtools/coverage) trong Công cụ của Chrome cho nhà phát triển. Bạn có thể xác định giao diện/mô-đun gây ra tình trạng này từ URL của biểu định kiểu khi tính năng tổng hợp CSS bị tắt trên trang web `Drupal`. Hãy chú ý đến những giao diện/mô-đun có nhiều biểu định kiểu trong danh sách. Những giao diện/mô-đun này có nhiều màu đỏ trong mức độ sử dụng mã. Mỗi giao diện/mô-đun chỉ nên đính kèm một thư viện biểu định kiểu nếu thư viện đó thực sự được dùng trên trang."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "Hãy cân nhắc xoá những thành phần JavaScript không dùng đến và chỉ đính kèm các thư viện `<PERSON><PERSON><PERSON>` cần thiết vào trang hoặc thành phần liên quan trên trang. Vui lòng xem [tài liệu về Drupal](https://www.drupal.org/docs/develop/theming-drupal/adding-assets-css-js-to-a-drupal-theme-via-librariesyml#define) để biết thông tin chi tiết. Để xác định các thư viện đã đính kèm đang thêm JavaScript không liên quan, hãy thử chạy [mức độ sử dụng mã](https://developer.chrome.com/docs/devtools/coverage) trong Công cụ của Chrome cho nhà phát triển. Bạn có thể xác định giao diện/mô-đun gây ra tình trạng này từ URL của tập lệnh khi tính năng tổng hợp JavaScript bị tắt trên trang web `Drupal`. Hãy chú ý đến những giao diện/mô-đun có nhiều tập lệnh trong danh sách. Những giao diện/mô-đun này có nhiều màu đỏ trong mức độ sử dụng mã. Mỗi giao diện/mô-đun chỉ nên đính kèm một thư viện tập lệnh nếu thư viện đó thực sự được dùng trên trang."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "Đặt \"Browser and proxy cache maximum age\" (<PERSON>h<PERSON><PERSON> gian tồn tại tối đa của bộ nhớ đệm proxy và trình duyệt) trê<PERSON> trang \"Administration » Configuration » Development\" (<PERSON><PERSON>ản trị > <PERSON><PERSON><PERSON> hình > <PERSON><PERSON><PERSON> triển). H<PERSON><PERSON> đọc về [Bộ nhớ đệm `Drupal` và tối ưu hoá cho hiệu suất](https://www.drupal.org/docs/8/api/cache-api/cache-api)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "Hãy cân nhắc sử dụng [một mô-đun](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=image&solrsort=iss_project_release_usage+desc&op=Search) tự động tối ưu hoá và giảm kích thước của hình ảnh tải lên qua trang web trong khi vẫn giữ được chất lượng. Ngoài ra, hãy đảm bảo bạn đang sử dụng [Kiểu hình ảnh thích ứng](https://www.drupal.org/documentation/modules/responsive_image) gốc do `Drupal` cung cấp cho tất cả hình ảnh hiển thị trên trang web."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "Bạn có thể thêm gợi ý về tài nguyên `Preconnect` hoặc `dns-prefetch` bằng cách cài đặt và định cấu hình [một mô-đun](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=Preconnect&solrsort=score+desc&op=Search) cung cấp các công cụ gợi ý tài nguyên cho tác nhân người dùng."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "<PERSON><PERSON><PERSON> bảo bạn đang sử dụng [<PERSON><PERSON><PERSON> hình ảnh thích ứng](https://www.drupal.org/documentation/modules/responsive_image) gốc do `Drupal` cung cấp. H<PERSON>y sử dụng Kiểu hình ảnh thích ứng khi hiển thị các trường hình ảnh thông qua chế độ xem, giao diện xem hoặc hình ảnh tải lên bằng trình chỉnh sửa WYSIWYG."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-text-compression": {"message": "Phải phân phát các tài nguyên dựa trên văn bản ở định dạng nén (gzip, deflate hoặc brotli) để giảm thiểu tổng số byte mạng. Hãy cân nhắc sử dụng một CDN vốn hỗ trợ tính năng này hoặc định cấu hình máy chủ web để thực hiện thao tác này. [Tìm hiểu thêm](https://developers.google.com/web/tools/lighthouse/audits/text-compression)"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "Dùng [Ezoic Leap](https://pubdash.ezoic.com/speed) và bật `Optimize Fonts` để tự động tận dụng tính năng CSS `font-display` nhằm đảm bảo người dùng thấy được văn bản trong lúc phông chữ web đang tải."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "Dùng [Ezoic Leap](https://pubdash.ezoic.com/speed) và bật `Next-Gen Formats` để chuyển đổi định dạng hình ảnh thành WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "Dùng [Ezoic Leap](https://pubdash.ezoic.com/speed) và bật `Lazy Load Images` để hoãn quá trình tải hình ảnh ngoài màn hình cho đến khi cần những hình ảnh đó."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "Dùng [Ezoic Leap](https://pubdash.ezoic.com/speed) đồng thời bật `Critical CSS` và `Script Delay` để hoãn mọi JS/CSS không thiết yếu."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "Dùng [Ezoic Cloud Caching](https://pubdash.ezoic.com/speed/caching) để lưu vào bộ nhớ đệm các nội dung của bạn trên mạng toàn cầu của chúng tôi, qua đó làm giảm thời gian tải byte đầu tiên."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "Dùng [Ezoic Leap](https://pubdash.ezoic.com/speed) và bật `Minify CSS` để tự động rút gọn CSS nhằm giảm dung lượng dữ liệu truyền qua mạng."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "Dùng [Ezoic Leap](https://pubdash.ezoic.com/speed) và bật `Minify Javascript` để tự động rút gọn JS nhằm giảm dung lượng dữ liệu truyền qua mạng."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "Dùng [Ezoic Leap](https://pubdash.ezoic.com/speed) và bật `Remove Unused CSS` để khắc phục vấn đề này. Cách làm này sẽ xác định các loại CSS đang được dùng trên mỗi trang thuộc trang web của bạn và xoá tất cả những loại còn lại để giảm kích thước tệp."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "Dùng [Ezoic Leap](https://pubdash.ezoic.com/speed) và bật `Efficient Static Cache Policy` để thiết lập giá trị đề xuất trong tiêu đề bộ nhớ đệm cho các tài sản tĩnh."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "Dùng [Ezoic Leap](https://pubdash.ezoic.com/speed) và bật `Next-Gen Formats` để chuyển đổi định dạng hình ảnh thành WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "Dùng [Ezoic Leap](https://pubdash.ezoic.com/speed) và bật `Pre-Connect Origins` để tự động thêm dấu hiệu tài nguyên `preconnect` nhằm thiết lập kết nối sớm với các nguồn gốc quan trọng của bên thứ ba."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "Dùng [Ezoic Leap](https://pubdash.ezoic.com/speed) đồng thời bật `Preload Fonts` và `Preload Background Images` để thêm các đường liên kết `preload` nhằm ưu tiên việc tìm nạp tài nguyên đang được yêu cầu trong lúc tải trang."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "Dùng [Ezoic Leap](https://pubdash.ezoic.com/speed) và bật `Resize Images` để thay đổi kích thước hình ảnh sao cho phù hợp với thiết bị, qua đó làm giảm dung lượng dữ liệu truyền qua mạng."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | modern-image-formats": {"message": "<PERSON>ử dụng thành phần `gatsby-plugin-image` thay cho `<img>` để tự động tối ưu hoá định dạng hình ảnh. [Tìm hiểu thêm](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | offscreen-images": {"message": "<PERSON>ử dụng thành phần `gatsby-plugin-image` thay cho `<img>` để tự động tải từng phần của hình <PERSON>nh. [Tìm hiểu thêm](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | prioritize-lcp-image": {"message": "Sử dụng thành phần `gatsby-plugin-image` và đặt thuộc tính `loading` thành `eager`. [Tìm hiểu thêm](https://www.gatsbyjs.com/docs/reference/built-in-components/gatsby-plugin-image#shared-props)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | render-blocking-resources": {"message": "Sử dụng `Gatsby Script API` để trì hoãn việc tải các tập lệnh không quan trọng của bên thứ ba. [Tìm hiểu thêm](https://www.gatsbyjs.com/docs/reference/built-in-components/gatsby-script/)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | unused-css-rules": {"message": "Sử dụng trình bổ trợ `PurgeCSS` `Gatsby` để xoá các quy tắc không sử dụng khỏi biểu định kiểu. [Tìm hiểu thêm](https://purgecss.com/plugins/gatsby.html)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | unused-javascript": {"message": "Sử dụng `Webpack Bundle Analyzer` để phát hiện mã JavaScript không được sử dụng. [Tìm hiểu thêm](https://www.gatsbyjs.com/plugins/gatsby-plugin-webpack-bundle-analyser-v2/)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-long-cache-ttl": {"message": "<PERSON><PERSON><PERSON> cấu hình bộ nhớ đệm cho các nội dung không thay đổi được. [Tìm hiểu thêm](https://www.gatsbyjs.com/docs/how-to/previews-deploys-hosting/caching/)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-optimized-images": {"message": "Sử dụng thành phần `gatsby-plugin-image` thay cho `<img>` để điều chỉnh chất lượng ảnh. [Tìm hiểu thêm](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-responsive-images": {"message": "<PERSON><PERSON> dụng thành phần `gatsby-plugin-image` để đặt `sizes` phù hợp. [Tìm hiểu thêm](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "<PERSON><PERSON><PERSON> cân nhắc tải ảnh GIF lên một dịch vụ để có thể nhúng ảnh đó ở dạng video HTML5."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "<PERSON><PERSON><PERSON> cân nhắc sử dụng một [plugin](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) hoặc dịch vụ tự động chuyển các hình ảnh đã tải lên sang định dạng tối ưu."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "Hãy cài đặt một [trình bổ trợ tải từng phần của <PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading) giúp trì hoãn mọi hình ảnh ngoài màn hình, hoặc chuyển sang một mẫu cung cấp chức năng đó. Kể từ Joomla 4.0, mọi hình ảnh mới sẽ [tự động](https://github.com/joomla/joomla-cms/pull/30748) có thuộc tính`loading` nhận được từ lõi."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "Một số trình bổ trợ của Jo<PERSON>la có thể giúp bạn [đưa các tài sản quan trọng vào cùng dòng](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) hoặc [trì hoãn những tài nguyên ít quan trọng hơn](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance). Hãy lưu ý rằng những tùy chọn tối ưu hóa mà các trình bổ trợ này cung cấp có thể phá hỏng tính năng của mẫu hoặc trình bổ trợ. Do đó, bạn cần thử nghiệm kỹ lưỡng các tùy chọn này."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "Mẫu, tiện ích và thông số máy chủ đều tác động đến thời gian phản hồi của máy chủ. H<PERSON>y cân nhắc tìm một mẫu tối ưu hóa hơn, lựa chọn cẩn thận tiện ích tối ưu hóa và/hoặc nâng cấp máy chủ."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "<PERSON><PERSON><PERSON> cân nhắc hiển thị phần trích dẫn trong danh mục bài viết của bạn (ví dụ: thông qua đường liên kết đọc thêm), gi<PERSON><PERSON> số lượng bài viết hiển thị trên một trang cụ thể, chia các bài viết dài thành nhiều trang hoặc sử dụng một trình bổ trợ để tải nhận xét theo từng phần."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "<PERSON><PERSON>t số [tiệ<PERSON> <PERSON><PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) có thể làm tăng tốc độ của trang web bằng cách ghép nối, gi<PERSON><PERSON> kích thước và nén các kiểu CSS. Ngoài ra, còn có các mẫu cung cấp chức năng này."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "<PERSON><PERSON>t số [tiệ<PERSON> <PERSON><PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) có thể làm tăng tốc độ của trang web bằng cách ghép nối, gi<PERSON><PERSON> kích thước và nén các tập lệnh. <PERSON><PERSON><PERSON><PERSON> ra, còn có các mẫu cung cấp chức năng này."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "Hãy cân nhắc giảm hoặc chuyển đổi số lượng [tiện ích Joomla](https://extensions.joomla.org/) tải CSS không dùng đến trên trang của bạn. Để xác định các tiện ích đang thêm CSS không liên quan, hãy thử chạy [phạm vi của mã](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) trong Chrome DevTools. Bạn có thể xác định giao diện/trình bổ trợ gây ra tình trạng này từ URL của tệp định kiểu. Hãy chú ý đến những trình bổ trợ có nhiều tệp định kiểu trong danh sách. Những trình bổ trợ này có nhiều màu đỏ trong phạm vi của mã. Mỗi trình bổ trợ chỉ nên thêm một tệp định kiểu vào hàng đợi nếu tệp định kiểu đó thực sự được sử dụng trên trang."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "Hãy cân nhắc giảm hoặc chuyển đổi số lượng [tiện ích Joomla](https://extensions.joomla.org/) tải các tệp JavaScript không dùng đến trên trang của bạn. Đ<PERSON> xác định các trình bổ trợ đang thêm JavaScript không liên quan, hãy thử chạy [phạm vi của mã](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) trong Chrome DevTools. Bạn có thể xác định tiện ích gây ra tình trạng này từ URL của tập lệnh. Hãy chú ý đến những tiện ích có nhiều tập lệnh trong danh sách. Những tiện ích này có nhiều màu đỏ trong phạm vi của mã. Mỗi tiện ích chỉ nên thêm một tập lệnh vào hàng đợi nếu tập lệnh đó thực sự được sử dụng trên trang."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "<PERSON><PERSON><PERSON> đọc về tính năng [<PERSON><PERSON><PERSON> vào bộ nhớ đệm của trình duyệt trong Joomla](https://docs.joomla.org/Cache)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "<PERSON><PERSON><PERSON> cân nhắc sử dụng một [trình bổ trợ tối ưu hóa hình ảnh](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) có khả năng nén hình ảnh mà vẫn giữ được chất lượng."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "<PERSON><PERSON><PERSON> cân nhắc dùng một [trình bổ trợ hình ảnh thích ứng](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images) để sử dụng các hình ảnh thích ứng trong nội dung của bạn."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "Bạn có thể bật tính năng nén văn bản bằng cách bật tính năng Nén trang Gzip trong Joomla (System > Global configuration > Server (<PERSON><PERSON> thống > <PERSON><PERSON><PERSON> hình chung > <PERSON><PERSON><PERSON> chủ))."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "<PERSON><PERSON><PERSON> bạn hiện không nhóm các nội dung JavaScript của mình, hãy cân nhắc dùng [trình đóng gói](https://github.com/magento/baler)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "Tắt tù<PERSON> chọ<PERSON> [gi<PERSON>m kích thước và nhóm JavaScript](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) tích hợ<PERSON> c<PERSON>, đồng thời cân nhắc dùng [trình đóng gói](https://github.com/magento/baler/)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "Chỉ định `@font-display` khi [xác định phông chữ tùy chỉnh](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "<PERSON><PERSON> nhắc tìm kiếm nhiều tiện ích của bên thứ ba trên [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=webp) để tận dụng những định dạng hình ảnh mới."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "C<PERSON> nhắc sửa đổi mẫu danh mục và sản phẩm để tận dụng tính năng [tải từng phần](https://web.dev/native-lazy-loading) của nền tảng web."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "<PERSON><PERSON> dụng t<PERSON>h năng [tích hợp Varnish](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html) c<PERSON><PERSON>."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> chọn \"<PERSON><PERSON><PERSON><PERSON> kích thướ<PERSON> tệp CSS\" trong phần cài đặt dành cho Nhà phát triển của cửa hàng. [Tìm hiểu thêm](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "<PERSON>ử dụng [Terser](https://www.npmjs.com/package/terser) để giảm kích thước của tất cả tài sản JavaScript trong quá trình triển khai nội dung tĩnh, đồng thời tắt tính năng giảm kích thước tích hợp."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "Tắt tùy chọn [nhóm JavaScript](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) tích hợp c<PERSON>a <PERSON>."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "<PERSON><PERSON> nhắc tìm kiếm nhiều tiện ích của bên thứ ba trên [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) để tối ưu hóa hình ảnh."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "<PERSON><PERSON> thể thêm các gợi ý về tài nguyên kết nối trước hoặc tìm nạp DNS trước bằng cách [sửa đổi bố cục của một giao diện](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "<PERSON><PERSON> thể thêm thẻ `<link rel=preload>` bằng cách [sửa đổi bố cục của một giao di<PERSON>n](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "Sử dụng thành phần `next/image` thay cho `<img>` để tự động tối ưu hoá định dạng hình ảnh. [Tìm hiểu thêm](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "Sử dụng thành phần `next/image` thay cho `<img>` để tự động tải từng phần của hình <PERSON>nh. [Tìm hiểu thêm](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "Dùng thành phần `next/image` và thiết lập gi<PERSON> trị \"mức độ ưu tiên\" thành đúng (true) để tải trước hình ảnh LCP. [Tìm hiểu thêm](https://nextjs.org/docs/api-reference/next/image#priority)."}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "Ha<PERSON>y dùng phần tử `next/script` để trì hoãn việc tải các tập lệnh không quan trọng của bên thứ ba. [Tìm hiểu thêm](https://nextjs.org/docs/basic-features/script)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "<PERSON>ử dụng thành phần `next/image` để đảm bảo hình ảnh luôn có kích thước phù hợp. [Tìm hiểu thêm](https://nextjs.org/docs/api-reference/next/image#width)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "Ha<PERSON><PERSON> cân nhắc đặt `PurgeCSS` trong cấu hình `Next.js` để xoá các quy tắc không sử dụng khỏi biểu định kiểu. [Tìm hiểu thêm](https://purgecss.com/guides/next.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "Sử dụng `Webpack Bundle Analyzer` để phát hiện mã JavaScript không được sử dụng. [Tìm hiểu thêm](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "<PERSON><PERSON><PERSON> cân nhắc dùng `Next.js Analytics` để đo lường hiệu suất của ứng dụng trong thực tế. [Tìm hiểu thêm](https://nextjs.org/docs/advanced-features/measuring-performance)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "<PERSON><PERSON><PERSON> cấu hình bộ nhớ đệm cho các trang `Server-side Rendered` (SSR) và thành phần không thay đổi được. [Tìm hiểu thêm](https://nextjs.org/docs/going-to-production#caching)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "Sử dụng thành phần `next/image` thay cho `<img>` để điều chỉnh chất lượng ảnh. [Tìm hiểu thêm](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "Ha<PERSON>y dùng phần tử `next/image` để đặt `sizes` phù hợp. [Tìm hiểu thêm](https://nextjs.org/docs/api-reference/next/image#sizes)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "<PERSON><PERSON><PERSON> bật t<PERSON>h năng nén trên máy chủ Next.js của bạn. [Tìm hiểu thêm](https://nextjs.org/docs/api-reference/next.config.js/compression)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | dom-size": {"message": "<PERSON><PERSON><PERSON> hệ với người quản lý tài khoản để bật [`HTML Lazy Load`](https://support.nitropack.io/hc/en-us/articles/17144942904337). Vi<PERSON><PERSON> định cấu hình tính năng này sẽ ưu tiên và tối ưu hoá hiệu suất kết xuất trang."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | font-display": {"message": "Sử dụng lựa chọn [`Override Font Rendering Behavior`](https://support.nitropack.io/hc/en-us/articles/16547358865041) trong NitroPack để đặt giá trị mong muốn cho quy tắc hiển thị phông chữ CSS."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | modern-image-formats": {"message": "Sử dụng [`Image Optimization`](https://support.nitropack.io/hc/en-us/articles/16547237162513) để tự động chuyển đổi hình <PERSON>nh sang WebP."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | offscreen-images": {"message": "<PERSON><PERSON><PERSON> hoãn hình ảnh ngoài màn hình bằng cách bật [`Automatic Image Lazy Loading`](https://support.nitropack.io/hc/en-us/articles/12457493524369-NitroPack-Lazy-Loading-Feature-for-Images)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | render-blocking-resources": {"message": "Bật [`Remove render-blocking resources`](https://support.nitropack.io/hc/en-us/articles/13820893500049-How-to-Deal-with-Render-Blocking-Resources-in-NitroPack) trong NitroPack để rút ngắn thời gian tải ban đầu."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unminified-css": {"message": "Bật [`Minify resources`](https://support.nitropack.io/hc/en-us/articles/360061059394-Minify-Resources) trong chế độ cài đặt Lưu vào bộ nhớ đệm để giảm kích thước của các tệp CSS, HTML và JavaScript, cũng như rút ngắn thời gian tải."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unminified-javascript": {"message": "Bật [`Minify resources`](https://support.nitropack.io/hc/en-us/articles/360061059394-Minify-Resources) trong chế độ cài đặt Lưu vào bộ nhớ đệm để giảm kích thước của các tệp JS, HTML và CSS và rút ngắn thời gian tải."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unused-css-rules": {"message": "Bật [`Reduce Unused CSS`](https://support.nitropack.io/hc/en-us/articles/360020418457-Reduce-Unused-CSS) để loại bỏ các quy tắc CSS không áp dụng cho trang này."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unused-javascript": {"message": "<PERSON><PERSON><PERSON> cấu hình [`Delayed Scripts`](https://support.nitropack.io/hc/en-us/articles/1500002600942-Delayed-Scripts) trong NitroPack để trì hoãn việc tải tập lệnh cho đến khi cần thiết."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-long-cache-ttl": {"message": "<PERSON><PERSON><PERSON> cậ<PERSON> t<PERSON> năng [`Improve Server Response Time`](https://support.nitropack.io/hc/en-us/articles/1500002321821-Improve-Server-Response-Time) trong trình đơn `Caching` và điều chỉnh thời gian hết hạn bộ nhớ đệm của trang để cải thiện thời gian tải và trải nghiệm người dùng."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-optimized-images": {"message": "Tự động nén, tối ưu hoá và chuyển đổi hình ảnh thành WebP bằng cách bật chế độ cài đặt [`Image Optimization`](https://support.nitropack.io/hc/en-us/articles/14177271695121-How-to-serve-images-in-next-gen-formats-using-NitroPack)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-responsive-images": {"message": "Bật [`Adaptive Image Sizing`](https://support.nitropack.io/hc/en-us/articles/10123833029905-How-to-Enable-Adaptive-Image-Sizing-For-Your-Site) để giành quyền tối ưu hoá hình ảnh và làm cho hình ảnh khớp với kích thước của vùng chứa hiển thị hình ảnh trên tất cả thiết bị."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-text-compression": {"message": "<PERSON>ử dụng [`Gzip compression`](https://support.nitropack.io/hc/en-us/articles/13229297479313-Enabling-GZIP-compression) trong NitroPack để giảm kích thước của các tệp được gửi đến trình du<PERSON>."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "<PERSON><PERSON> dụng thành phần `nuxt/image` và thiết lập `format=\"webp\"`. [Tìm hiểu thêm](https://image.nuxt.com/usage/nuxt-img#format)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "Sử dụng thành phần `nuxt/image` và thiết lập `loading=\"lazy\"` cho hình ảnh ngoài màn hình. [Tìm hiểu thêm](https://image.nuxt.com/usage/nuxt-img#loading)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "Sử dụng thành phần `nuxt/image` và chỉ định `preload` cho hình ảnh LCP. [Tìm hiểu thêm](https://image.nuxt.com/usage/nuxt-img#preload)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "Sử dụng thành phần `nuxt/image` và chỉ định `width` và `height` một cách rõ ràng. [Tìm hiểu thêm](https://image.nuxt.com/usage/nuxt-img#width-height)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "Sử dụng thành phần `nuxt/image` và thiết lập `quality` phù hợp. [Tìm hiểu thêm](https://image.nuxt.com/usage/nuxt-img#quality)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "Sử dụng thành phần `nuxt/image` và thiết lập `sizes` phù hợp. [Tìm hiểu thêm](https://image.nuxt.com/usage/nuxt-img#sizes)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "[Thay thế ảnh GIF động bằng video](https://web.dev/replace-gifs-with-videos/) để tải trang web n<PERSON><PERSON> hơn, đồng thời cân nhắc sử dụng các định dạng tệp hiện đại nh<PERSON> [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos) hay [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder) để cải thiện hiệu quả nén thêm hơn 30% so với codec video tiên tiến hiện tại là VP9."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "Hãy cân nhắc sử dụng một [trình bổ trợ](https://octobercms.com/plugins?search=image) hoặc dịch vụ tự động chuyển đổi các hình ảnh đã tải lên sang định dạng tối ưu. [Hình ảnh ở định dạng WebP không suy hao](https://developers.google.com/speed/webp) có kích thước nhỏ hơn 26% so với hình ảnh ở định dạng PNG và 25 – 34% so với hình ảnh JPEG tương đương với chỉ số chất lượng SSIM như nhau. Một định dạng hình ảnh mới khác nên cân nhắc là [AVIF](https://jakearchibald.com/2020/avif-has-landed/)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "Hãy cân nhắc cài đặt một [trình bổ trợ giúp tải hình ảnh theo từng phần](https://octobercms.com/plugins?search=lazy) để trì hoãn mọi hình ảnh ngoài màn hình, hoặc chuyển sang một giao diện cung cấp chức năng đó. <PERSON><PERSON><PERSON><PERSON> ra, hãy cân nhắc sử dụng [trình bổ trợ Accelerated Mobile Pages (AMP)](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "<PERSON><PERSON> nhiều trình bổ trợ giúp [đưa các tài sản quan trọng vào cùng dòng](https://octobercms.com/plugins?search=css). Các trình bổ trợ này có thể phá hỏng những trình bổ trợ khác. <PERSON> đ<PERSON>, bạn nên thử nghiệm kỹ lưỡng."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "<PERSON><PERSON><PERSON>, tr<PERSON><PERSON> bổ trợ và thông số máy chủ đều tác động đến thời gian phản hồi của máy chủ. Hãy cân nhắc tìm một giao diện tối ưu hóa hơn, lựa chọn cẩn thận trình bổ trợ tối ưu hóa và/hoặc nâng cấp máy chủ. October CMS cũng cho phép nhà phát triển dùng [`Queues`](https://octobercms.com/docs/services/queues) để trì hoãn quá trình xử lý một nhiệm vụ tốn nhiều thời gian, chẳng hạn như gửi email. Điều này giúp tăng tốc đáng kể cho các yêu cầu web."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "<PERSON><PERSON><PERSON> cân nhắc hiển thị phần trích dẫn trong danh sách bài đăng (ví dụ: sử dụng nút `show more`), gi<PERSON>m số lượng bài đăng hiển thị trên một trang web cụ thể, chia các bài đăng dài thành nhiều trang web hoặc sử dụng một trình bổ trợ để tải bình luận theo từng phần."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "<PERSON><PERSON> nhiều [trình bổ trợ](https://octobercms.com/plugins?search=css) có thể làm tăng tốc độ của trang web bằng cách ghép nối, gi<PERSON><PERSON> kích thước và nén các kiểu. Việc sử dụng quy trình xây dựng để thực hiện trước quá trình giảm kích thước này có thể đẩy nhanh tốc độ phát triển."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "<PERSON><PERSON> nhiều [trình bổ trợ](https://octobercms.com/plugins?search=javascript) có thể làm tăng tốc độ của trang web bằng cách ghép nối, gi<PERSON><PERSON> kích thước và nén các tập lệnh. Việc sử dụng quy trình xây dựng để thực hiện trước quá trình giảm kích thước này có thể đẩy nhanh tốc độ phát triển."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "Hãy xem xét những [trình bổ trợ](https://octobercms.com/plugins) tải tệp CSS không dùng đến trên trang web. Đ<PERSON> xác định các trình bổ trợ thêm tệp CSS không cần thiết, hãy chạy phần [mức độ sử dụng mã](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) trong công cụ cho nhà phát triển của Chrome. Bạn có thể xác định giao diện/trình bổ trợ gây ra tình trạng này từ URL biểu định kiểu. Hãy tìm các trình bổ trợ chứa nhiều biểu định kiểu có nhiều màu đỏ trong phần mức độ sử dụng mã. Mỗi trình bổ trợ chỉ nên thêm một biểu định kiểu nếu biểu định kiểu đó thực sự được sử dụng trên trang web."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "Hãy xem xét những [trình bổ trợ](https://octobercms.com/plugins?search=javascript) tải các tệp JavaScript không dùng đến trên trang web. Đ<PERSON> xác định các trình bổ trợ thêm tệp JavaScript không cần thiết, hãy chạy phần [mức độ sử dụng mã](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) trong công cụ cho nhà phát triển của Chrome. Bạn có thể xác định giao diện/trình bổ trợ gây ra tình trạng này từ URL của tập lệnh. Hãy tìm các trình bổ trợ chứa nhiều tập lệnh có nhiều màu đỏ trong phần mức độ sử dụng mã. Mỗi trình bổ trợ chỉ nên thêm một tập lệnh nếu tập lệnh đó thực sự được sử dụng trên trang web."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "<PERSON><PERSON><PERSON> đ<PERSON> c<PERSON>ch [tránh những yêu cầu không cần thiết về mạng nhờ Bộ nhớ đệm HTTP](https://web.dev/http-cache/#caching-checklist). <PERSON><PERSON> [trình bổ trợ](https://octobercms.com/plugins?search=Caching) mà bạn có thể dùng để tăng tốc độ lưu vào bộ nhớ đệm."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "<PERSON><PERSON><PERSON> cân nhắc sử dụng một [trình bổ trợ tối ưu hóa hình ảnh](https://octobercms.com/plugins?search=image) để nén hình ảnh mà vẫn giữ được chất lượng."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "Trực tiếp tải hình ảnh lên trong trình quản lý nội dung nghe nhìn để đảm bảo có sẵn các kích thước hình ảnh theo yêu cầu. Hãy cân nhắc dùng [bộ lọc đổi kích thước](https://octobercms.com/docs/markup/filter-resize) hoặc một [trình bổ trợ đổi kích thước hình ảnh](https://octobercms.com/plugins?search=image) để đảm bảo sử dụng kích thước hình ảnh tối ưu."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "<PERSON><PERSON><PERSON> t<PERSON>h năng nén văn bản trong cấu hình máy chủ web."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "H<PERSON><PERSON> cân nhắc dùng một thư viện \"t<PERSON><PERSON> cửa sổ\" như `react-window` để giảm thiểu số nút DOM được tạo nếu bạn đang hiển thị nhiều phần tử lặp lại trên trang. [Tìm hiểu thêm](https://web.dev/virtualize-long-lists-react-window/). <PERSON><PERSON><PERSON> thời, hãy giảm thiểu các thao tác hiển thị lại không cần thiết bằng [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent) hoặc [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo) và tùy chọn [bỏ qua hiệu ứng](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects) cho đến khi một số phần phụ thuộc thay đổi, nếu bạn đang dùng hook `Effect` để cải thiện hiệu suất trong thời gian chạy."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "<PERSON><PERSON><PERSON> bạn đang dùng React Router, h<PERSON><PERSON> gi<PERSON><PERSON> thiể<PERSON> mức sử dụng thành phần `<Redirect>` để [định tuyến](https://reacttraining.com/react-router/web/api/Redirect)."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "Nếu bạn đang hiển thị bất kỳ thành phần React nào ở phía máy chủ, hãy cân nhắc dùng `renderToPipeableStream()` hoặc `renderToStaticNodeStream()` để cho phép ứng dụng nhận và nhập những phần khác nhau của mục đánh dấu thay vì tất cả cùng lúc. [Tìm hiểu thêm](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "<PERSON><PERSON><PERSON> hệ thống bản dựng của bạn tự động giảm kích thước tệp CSS, hãy đảm bảo rằng bạn đang triển khai bản phát hành chính thức của ứng dụng. Bạn có thể kiểm tra điều này bằng tiện ích React Developer Tools. [Tìm hiểu thêm](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "<PERSON><PERSON><PERSON> hệ thống bản dựng của bạn tự động giảm kích thước tệp JS, hãy đảm bảo rằng bạn đang triển khai bản phát hành chính thức của ứng dụng. Bạn có thể kiểm tra điều này bằng tiện ích React Developer Tools. [Tìm hiểu thêm](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "<PERSON><PERSON>u bạn không định hiển thị phía máy chủ, hã<PERSON> [phân tách các gói JavaScript](https://web.dev/code-splitting-suspense/) bằng `React.lazy()`. <PERSON><PERSON><PERSON> không, hãy phân tách mã bằng một thư viện của bên thứ ba như [các thành phần có thể tải](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "Sử dụng Trình phân tích tài nguyên React DevTools, công cụ dùng API Trình phân tích tài nguyên để đo lường hiệu suất hiển thị của các thành phần. [Tìm hiểu thêm.](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)"}, "node_modules/lighthouse-stack-packs/packs/wix.js | efficient-animated-content": {"message": "Đưa video vào `VideoBoxes`, tuỳ chỉnh video bằng `Video Masks` hoặc thêm `Transparent Videos`. [Tìm hiểu thêm](https://support.wix.com/en/article/wix-video-about-wix-video)."}, "node_modules/lighthouse-stack-packs/packs/wix.js | modern-image-formats": {"message": "T<PERSON>i hình ảnh lên bằng cách sử dụng `Wix Media Manager` để đảm bảo hình ảnh tự động được phân phát dưới dạng WebP. Tìm hiểu [các giải pháp khác giúp bạn tối ưu hoá](https://support.wix.com/en/article/site-performance-optimizing-your-media) nội dung nghe nhìn trên trang web."}, "node_modules/lighthouse-stack-packs/packs/wix.js | render-blocking-resources": {"message": "<PERSON><PERSON> bạn [thêm mã của bên thứ ba](https://support.wix.com/en/article/site-performance-using-third-party-code-on-your-site) vào thẻ `Custom Code` trên trang tổng quan của trang web, hãy đảm bảo mã đó được trì hoãn đến cuối nội dung mã hoặc tải ở cuối nội dung mã. Nếu có thể, hãy sử dụng [nội dung tích hợp](https://support.wix.com/en/article/about-marketing-integrations) của Wix để nhúng các công cụ tiếp thị vào trang web của bạn. "}, "node_modules/lighthouse-stack-packs/packs/wix.js | server-response-time": {"message": "Wix sử dụng CDN và chức năng lưu vào bộ nhớ đệm để phân phát phản hồi nhanh nhất có thể cho hầu hết khách truy cập. Bạn có thể [bật chức năng lưu vào bộ nhớ đệm theo cách thủ công](https://support.wix.com/en/article/site-performance-caching-pages-to-optimize-loading-speed) cho trang web của mình, đặc biệt là khi sử dụng `Velo`."}, "node_modules/lighthouse-stack-packs/packs/wix.js | unused-javascript": {"message": "Xem lại mã của bên thứ ba (nếu có) mà bạn đã thêm vào thẻ `Custom Code` trên trang tổng quan của trang web và chỉ giữ lại các dịch vụ cần thiết cho trang web của bạn. [Tìm hiểu thêm](https://support.wix.com/en/article/site-performance-removing-unused-javascript)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "<PERSON><PERSON><PERSON> cân nhắc tải ảnh GIF lên một dịch vụ để có thể nhúng ảnh đó ở dạng video HTML5."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "<PERSON><PERSON><PERSON> cân nhắc sử dụng trình bổ trợ [<PERSON>òng thí nghiệm hiệu suất](https://wordpress.org/plugins/performance-lab/) để tự động chuyển đổi hình ảnh JPEG đã tải lên thành WebP, trên mọi nền tảng được hỗ trợ."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "Cài đặt một [plugin tải từng phần của WordPress](https://wordpress.org/plugins/search/lazy+load/) để trì hoãn mọi hình ảnh ngoài màn hình, hoặc chuy<PERSON>n sang một giao diện cung cấp chức năng đó. <PERSON><PERSON><PERSON><PERSON> ra, hã<PERSON> cân nhắc sử dụng [plugin AMP (Accelerated Mobile Pages)](https://wordpress.org/plugins/amp/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "C<PERSON> một số plugin của WordPress có thể giúp bạn [đưa phần tử quan trọng vào nội tuyến](https://wordpress.org/plugins/search/critical+css/) hoặc [trì hoãn tài nguyên ít quan trọng hơn](https://wordpress.org/plugins/search/defer+css+javascript/). Xin lưu ý rằng những phương thức tối ưu hóa mà các plugin này cung cấp có thể làm gián đoạn tính năng của giao diện hoặc plugin, do đó, có thể bạn cần thay đổi mã."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "Chọn một giao diện gọn nhẹ (tốt nhất là giao diện khối) và triển khai tính năng lưu vào bộ nhớ đệm toàn trang hoặc giải pháp trang web tĩnh. Tắt các trình bổ trợ không cần thiết để giảm thiểu mức hao tổn máy chủ. Hãy cân nhắc nâng cấp dịch vụ lưu trữ lên dịch vụ được quản lý hoặc dịch vụ chuyên dụng."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "<PERSON><PERSON><PERSON> cân nhắc hiển thị phần trích dẫn trong danh sách bài đăng (ví dụ như qua thẻ thêm), gi<PERSON><PERSON> số lượng bài đăng hiển thị trên một trang cụ thể, chia các bài đăng dài thành nhiều trang hoặc sử dụng một plugin để tải nhận xét theo từng phần."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "Một số [plugin của WordPress](https://wordpress.org/plugins/search/minify+css/) có thể làm tăng tốc độ của trang web bằng cách ghép, gi<PERSON><PERSON> kích thước và nén kiểu. Bạn cũng có thể dùng quy trình tạo để tiến hành thu nhỏ trước, nếu có thể."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "Một số [plugin của WordPress](https://wordpress.org/plugins/search/minify+javascript/) có thể làm tăng tốc độ của trang web bằng cách ghép, gi<PERSON><PERSON> kích thước và nén tập lệnh. <PERSON><PERSON><PERSON><PERSON> ra, bạn nên dùng quy trình tạo để tiến hành thu nhỏ trước, nếu có thể."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "Hãy cân nhắc giảm hoặc chuyển đổi số lượng [plugin của WordPress](https://wordpress.org/plugins/) tải Biểu định kiểu xếp chồng (CSS) không dùng đến trong trang của bạn. Để xác định các plugin đang thêm Biểu định kiểu xếp chồng (CSS) không liên quan, hãy thử chạy [phạm vi của mã](https://developer.chrome.com/docs/devtools/coverage/) trong Chrome DevTools. Bạn có thể xác định giao diện/plugin gây ra tình trạng này từ URL của biểu định kiểu. Hãy chú ý đến những plugin có nhiều biểu định kiểu trong danh sách. Những plugin này có nhiều màu đỏ trong phạm vi của mã. Một plugin chỉ nên thêm một biểu định kiểu vào hàng đợi nếu biểu định kiểu đó thực sự được sử dụng trên trang."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "Hãy cân nhắc giảm hoặc chuyển đổi số lượng [plugin của WordPress](https://wordpress.org/plugins/) tải JavaScript không dùng đến trong trang của bạn. Để xác định các plugin đang thêm JS không liên quan, hãy thử chạy [phạm vi của mã](https://developer.chrome.com/docs/devtools/coverage/) trong Chrome DevTools. Bạn có thể xác định giao diện/plugin gây ra tình trạng này từ URL của tập lệnh. Hãy chú ý đến những plugin có nhiều tập lệnh trong danh sách. Những plugin này có nhiều màu đỏ trong phạm vi của mã. Một plugin chỉ nên thêm một tập lệnh vào hàng đợi nếu tập lệnh đó thực sự được dùng trên trang."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "<PERSON><PERSON><PERSON> đ<PERSON> về [Bộ nhớ đệm của trình duyệt trong WordPress](https://wordpress.org/support/article/optimization/#browser-caching)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "<PERSON><PERSON><PERSON> cân nhắc sử dụng một [trình bổ trợ tối ưu hóa hình ảnh của WordPress](https://wordpress.org/plugins/search/optimize+images/) c<PERSON> khả năng nén hình ảnh mà vẫn đảm bảo chất lượ<PERSON>."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "Trực tiếp tải hình ảnh lên thông qua [thư viện nội dung đa phương tiện](https://wordpress.org/support/article/media-library-screen/) để đảm bảo có các kích thước hình ảnh theo yêu cầu, sau đó chèn hình ảnh từ thư viện nội dung đa phương tiện hoặc sử dụng tiện ích hình ảnh để đảm bảo sử dụng kích thước hình ảnh tối ưu (bao gồm cả các kích thước dành cho điểm ngắt thích ứng). Tránh sử dụng hình ảnh có `Full Size` trừ phi những hình ảnh đó có kích thước phù hợp. [Tìm hiểu thêm](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "<PERSON><PERSON>n có thể bật tính năng nén văn bản trong cấu hình máy chủ web."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "<PERSON><PERSON><PERSON> \"Imagify\" từ thẻ Tối ưu hoá hình ảnh trong \"WP Rocket\" để chuyển đổi hình ảnh sang định dạng WebP."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "Bật [LazyLoad](https://docs.wp-rocket.me/article/1141-lazyload-for-images) trong WP Rocket để khắc phục nội dung đề xuất này. T<PERSON>h năng này trì hoãn việc tải hình ảnh cho đến khi khách truy cập di chuyển xuống phía dưới trang và thực sự cần xem những hình ảnh đó."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "Bật tính năng [Xoá CSS không dùng đến](https://docs.wp-rocket.me/article/1529-remove-unused-css) và [Tải JavaScript bị trì hoãn](https://docs.wp-rocket.me/article/1265-load-javascript-deferred) trong \"WP Rocket\" để giải quyết nội dung đề xuất này. <PERSON>ác tính năng này sẽ tối ưu hoá lần lượt các tệp CSS và JavaScript để các tệp đó không chặn quá trình hiển thị trang."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "<PERSON><PERSON>t t<PERSON>h năng [<PERSON><PERSON><PERSON> gọn các tệp CSS](https://docs.wp-rocket.me/article/1350-css-minify-combine) trong \"WP Rocket\" để khắc phục vấn đề này. M<PERSON>i không gian và nhận xét trong tệp CSS trên trang web của bạn sẽ bị xoá để giảm kích thước tệp và đẩy nhanh tốc độ tải xuống."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "B<PERSON>t t<PERSON>h năng [<PERSON><PERSON><PERSON> gọn các tệp JavaScript](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) trong \"WP Rocket\" để khắc phục vấn đề này. Các không gian trống và nhận xét sẽ bị xoá khỏi tệp JavaScript để giảm kích thước tệp và tăng tốc độ tải xuống."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "Bật tính năng [Xoá CSS không dùng đến](https://docs.wp-rocket.me/article/1529-remove-unused-css) trong \"WP Rocket\" để khắc phục vấn đề này. Thao tác này làm giảm kích thước trang bằng cách xoá tất cả các CSS và biểu định kiểu không được sử dụng trong khi chỉ giữ lại CSS được sử dụng cho mỗi trang."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "<PERSON><PERSON>t t<PERSON>h năng [<PERSON><PERSON><PERSON> hoãn thực thi JavaScript](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) trong \"WP Rocket\" để khắc phục sự cố này. Thao tác này sẽ cải thiện việc tải trang bằng cách trì hoãn việc thực thi tập lệnh cho đến khi người dùng tương tác. Nếu trang web của bạn có iframe, bạn có thể sử dụng [LazyLoad của WP Rocket cho iframe và video](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos) cũng như [Thay thế iframe YouTube bằng hình ảnh xem trước](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "<PERSON><PERSON><PERSON> \"Imagify\" từ thẻ Tối ưu hoá hình ảnh trong \"WP Rocket\" rồi chạy tính năng Tối ưu hoá hàng loạt để nén hình ảnh."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "Dùng tính năng [Tì<PERSON> nạp trước yêu cầu DNS](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests) trong \"WP Rocket\" để thêm \"dns-prefetch\" và tăng tốc độ kết nối với các miền bên ngo<PERSON>. <PERSON><PERSON><PERSON><PERSON>a, \"WP Rocket\" tự động thêm \"preconnect\" vào [miền <PERSON> Fonts](https://docs.wp-rocket.me/article/1312-optimize-google-fonts) và mọi CNAME được thêm qua tính năng [Bật CDN](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "<PERSON><PERSON> khắc phục vấn đề về phông chữ này, h<PERSON><PERSON> bật tính năng [Xoá CSS không dùng đến](https://docs.wp-rocket.me/article/1529-remove-unused-css) trong \"WP Rocket\". Phông chữ quan trọng của trang web sẽ được tải trước theo mức độ ưu tiên."}, "report/renderer/report-utils.js | calculatorLink": {"message": "<PERSON><PERSON> m<PERSON>."}, "report/renderer/report-utils.js | collapseView": {"message": "<PERSON><PERSON> độ xem thu gọn"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "<PERSON><PERSON><PERSON><PERSON> hướng ban đầu"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "<PERSON><PERSON> trễ tối đa của đường dẫn quan trọng:"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "<PERSON>o chép đối tư<PERSON>ng JSON"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "Bật/tắt <PERSON> di<PERSON>n tối"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "Mở rộng trong hộp thoại in"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "<PERSON><PERSON>o cáo tóm tắt trong hộp tho<PERSON>i in"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "<PERSON><PERSON><PERSON> dạng <PERSON>"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "<PERSON><PERSON><PERSON> dạng HTML"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "<PERSON><PERSON><PERSON> dạng JSON"}, "report/renderer/report-utils.js | dropdownViewUnthrottledTrace": {"message": "<PERSON><PERSON> dấu vết không đ<PERSON><PERSON><PERSON> điều tiết"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "Mở trong trình xem"}, "report/renderer/report-utils.js | errorLabel": {"message": "Lỗi!"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "Lỗi báo cáo: không có thông tin kiểm tra"}, "report/renderer/report-utils.js | expandView": {"message": "<PERSON><PERSON> độ xem mở rộng"}, "report/renderer/report-utils.js | firstPartyChipLabel": {"message": "<PERSON><PERSON><PERSON> nh<PERSON>t"}, "report/renderer/report-utils.js | footerIssue": {"message": "<PERSON><PERSON><PERSON> vấn đề"}, "report/renderer/report-utils.js | goBackToAudits": {"message": "Quay lại phần kiểm tra"}, "report/renderer/report-utils.js | hide": {"message": "Ẩn"}, "report/renderer/report-utils.js | insightsNotice": {"message": "V<PERSON>o cuối năm nay, các báo cáo kiểm tra thông tin chi tiết sẽ thay thế cho báo cáo kiểm tra hiệu suất. [Tìm hiểu thêm và đưa ra ý kiến phản hồi tại đây](https://github.com/GoogleChrome/lighthouse/discussions/16462)."}, "report/renderer/report-utils.js | labDataTitle": {"message": "<PERSON><PERSON> liệu của phòng thí nghiệm"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "Kết quả phân tích [Lighthouse](https://developers.google.com/web/tools/lighthouse/) cho trang hiện tại dựa trên một mạng di động mô phỏng. Các giá trị chỉ là ước tính và có thể thay đổi."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> b<PERSON> sung cần kiểm tra theo cách thủ công"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> dụng"}, "report/renderer/report-utils.js | openInANewTabTooltip": {"message": "Mở trong thẻ mới"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "<PERSON><PERSON> hội"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "<PERSON>h<PERSON><PERSON> lượng tiết kiệm đư<PERSON><PERSON> theo <PERSON> t<PERSON>h"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "<PERSON><PERSON> lần kiểm tra đạt yêu cầu"}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "<PERSON><PERSON><PERSON><PERSON> tải trang đầu tiên"}, "report/renderer/report-utils.js | runtimeAnalysisWindowSnapshot": {"message": "Ảnh chụp nhanh tại một thời điểm cụ thể"}, "report/renderer/report-utils.js | runtimeAnalysisWindowTimespan": {"message": "<PERSON><PERSON><PERSON><PERSON> thời gian có hoạt động tương tác của người dùng"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "Chế độ điều tiết tuỳ chỉnh"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "<PERSON><PERSON><PERSON> hình mô phỏng"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "Moto G Power mô phỏng"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "Ch<PERSON>a đặt tùy chọn mô phỏng"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "<PERSON><PERSON><PERSON> b<PERSON>"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "<PERSON>ông suất CPU/bộ nhớ không đư<PERSON><PERSON> điều tiết"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "<PERSON><PERSON><PERSON><PERSON> bị"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "<PERSON><PERSON><PERSON> chế băng thông mạng"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "<PERSON><PERSON> phỏng màn hình"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "<PERSON><PERSON><PERSON> n<PERSON>ân ng<PERSON><PERSON>i dùng (mạng)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "<PERSON><PERSON><PERSON> trang đơn"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "Dữ liệu này đư<PERSON>c lấy trong một phiên trang đơn, kh<PERSON>c với dữ liệu thực địa tóm tắt nhiều phiên."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "Chế độ điều tiết mạng 4G chậm"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>"}, "report/renderer/report-utils.js | show": {"message": "<PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "<PERSON><PERSON><PERSON> thị các l<PERSON> kiểm tra liên quan đến:"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "<PERSON><PERSON> gọn đoạn mã"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "Mở rộng đoạn mã"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "<PERSON><PERSON>n thị tài nguyên của bên thứ ba"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "<PERSON><PERSON> cấp theo môi trường"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "<PERSON><PERSON> xảy ra sự cố ảnh hưởng đến lần chạy Lighthouse này:"}, "report/renderer/report-utils.js | tryInsights": {"message": "<PERSON><PERSON><PERSON> t<PERSON>h năng kiểm tra thông tin chi tiết"}, "report/renderer/report-utils.js | unattributable": {"message": "<PERSON><PERSON><PERSON><PERSON> thể phân bổ"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "<PERSON><PERSON><PERSON> giá trị chỉ là ước tính và có thể thay đổi. [Điểm hiệu quả được tính](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) trực tiếp từ những chỉ số này."}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "<PERSON><PERSON> v<PERSON>"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "<PERSON><PERSON> đồ dạng cây"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "<PERSON><PERSON> vượt qua bài kiểm tra nhưng có cảnh báo"}, "report/renderer/report-utils.js | warningHeader": {"message": "Cảnh báo: "}, "treemap/app/src/util.js | allLabel": {"message": "<PERSON><PERSON><PERSON> c<PERSON>"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "<PERSON><PERSON><PERSON> cả tập lệnh"}, "treemap/app/src/util.js | coverageColumnName": {"message": "<PERSON><PERSON> bao phủ"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "<PERSON><PERSON><PERSON>đun trùng lặp"}, "treemap/app/src/util.js | duplicatedBytesLabel": {"message": "Số byte trùng lặp"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "Số byte tài nguyên"}, "treemap/app/src/util.js | tableColumnName": {"message": "<PERSON><PERSON><PERSON>"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "Ẩn/hiện bảng"}, "treemap/app/src/util.js | transferBytesLabel": {"message": "Số byte c<PERSON>n ch<PERSON>n"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "Số byte chưa dùng đến"}}