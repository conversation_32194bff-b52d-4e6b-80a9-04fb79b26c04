// NOTICE: This file is generated by Rollup. To modify it,
// please instead edit the ESM counterpart and rebuild with Rollup (npm run build).
'use strict';

const path = require('node:path');
const process = require('node:process');
const createStylelint = require('./createStylelint.cjs');
const validateTypes = require('./utils/validateTypes.cjs');
const lintSource = require('./lintSource.cjs');

/** @import {Config as StylelintConfig, PostcssPluginOptions} from 'stylelint' */

/**
 * @type {import('postcss').PluginCreator<PostcssPluginOptions>}
 */
function postcssPlugin(options = {}) {
	const [cwd, tailoredOptions] = isConfig(options)
		? [process.cwd(), { config: options }]
		: [('cwd' in options && validateTypes.isString(options.cwd) && options.cwd) || process.cwd(), options];
	const stylelint = createStylelint(tailoredOptions);

	return {
		postcssPlugin: 'stylelint',

		/**
		 * @param {import('postcss').Root} root
		 * @param {import('postcss').Helpers} helpers
		 * @returns {Promise<void>}
		 */
		async Once(root, { result }) {
			let filePath = root.source && root.source.input.file;

			if (filePath && !path.isAbsolute(filePath)) {
				filePath = path.join(cwd, filePath);
			}

			await lintSource(stylelint, {
				filePath,
				existingPostcssResult: result,
			});
		},
	};
}

postcssPlugin.postcss = true;

/**
 * @param {PostcssPluginOptions} options
 * @returns {options is StylelintConfig}
 */
function isConfig(options) {
	return 'rules' in options;
}

module.exports = postcssPlugin;
