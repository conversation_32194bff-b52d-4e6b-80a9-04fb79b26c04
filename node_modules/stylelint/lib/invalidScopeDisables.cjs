// NOTICE: This file is generated by Rollup. To modify it,
// please instead edit the ESM counterpart and rebuild with Rollup (npm run build).
'use strict';

const constants = require('./constants.cjs');
const optionsMatches = require('./utils/optionsMatches.cjs');
const reportCommentProblem = require('./utils/reportCommentProblem.cjs');
const validateDisableSettings = require('./validateDisableSettings.cjs');

/**
 * @param {import('stylelint').PostcssResult} postcssResult
 * @returns {void}
 */
function invalidScopeDisables(postcssResult) {
	const [enabled, options] = validateDisableSettings(postcssResult, 'reportInvalidScopeDisables');

	if (!options) return;

	const configRules = postcssResult.stylelint.config?.rules;

	if (!configRules) return;

	const usedRules = new Set(Object.keys(configRules));

	usedRules.add(constants.RULE_NAME_ALL);

	for (const [rule, ruleRanges] of Object.entries(postcssResult.stylelint.disabledRanges)) {
		if (usedRules.has(rule)) continue;

		if (enabled === optionsMatches(options, 'except', rule)) continue;

		for (const range of ruleRanges) {
			if (!range.strictStart && !range.strictEnd) continue;

			reportCommentProblem({
				rule: '--report-invalid-scope-disables',
				message: `Rule "${rule}" isn't enabled`,
				severity: options.severity,
				node: range.node,
				postcssResult,
			});
		}
	}
}

module.exports = invalidScopeDisables;
