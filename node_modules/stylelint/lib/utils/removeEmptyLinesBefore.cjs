// NOTICE: This file is generated by Rollup. To modify it,
// please instead edit the ESM counterpart and rebuild with Rollup (npm run build).
'use strict';

/**
 * Remove empty lines before a node. Mutates the node.
 *
 * @template {import('postcss').Node} T
 * @param {T} node
 * @param {string} newline
 * @returns {T}
 */
function removeEmptyLinesBefore(node, newline) {
	node.raws.before = node.raws.before ? node.raws.before.replace(/(\r?\n\s*\n)+/g, newline) : '';

	return node;
}

module.exports = removeEmptyLinesBefore;
