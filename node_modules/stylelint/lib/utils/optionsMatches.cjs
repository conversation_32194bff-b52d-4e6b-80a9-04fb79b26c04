// NOTICE: This file is generated by Rollup. To modify it,
// please instead edit the ESM counterpart and rebuild with Rollup (npm run build).
'use strict';

const matchesStringOrRegExp = require('./matchesStringOrRegExp.cjs');

/**
 * Check if an options object's propertyName contains a user-defined string or
 * regex that matches the passed in input.
 *
 * @param {{ [x: string]: any; }} options
 * @param {string} propertyName
 * @param {unknown} input
 *
 * @returns {boolean}
 */
function optionsMatches(options, propertyName, input) {
	return Boolean(
		options &&
			options[propertyName] &&
			typeof input === 'string' &&
			matchesStringOrRegExp(input, options[propertyName]),
	);
}

module.exports = optionsMatches;
