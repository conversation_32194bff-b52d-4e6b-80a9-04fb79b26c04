// NOTICE: This file is generated by Rollup. To modify it,
// please instead edit the ESM counterpart and rebuild with Rollup (npm run build).
'use strict';

const mediaQueryListParser = require('@csstools/media-query-list-parser');
const getAtRuleParams = require('./getAtRuleParams.cjs');

/**
 * @param {import('postcss').AtRule} atRule
 * @returns {ReturnType<typeof import('@csstools/media-query-list-parser').parse>}
 */
function parseMediaQuery(atRule) {
	const mediaQueries = mediaQueryListParser.parse(getAtRuleParams(atRule), {
		preserveInvalidMediaQueries: true,
	});

	return mediaQueries;
}

module.exports = parseMediaQuery;
