// NOTICE: This file is generated by Rollup. To modify it,
// please instead edit the ESM counterpart and rebuild with Rollup (npm run build).
'use strict';

/**
 * Check if two ranges of source offsets overlap.
 * This function assumes that the provided ranges have a width of at least one column.
 *
 * @param {[number, number]} a
 * @param {[number, number]} b
 * @returns {boolean}
 */
function rangesOverlap(a, b) {
	// a: ----
	// b:      ----
	if (a[1] <= b[0]) return false;

	// a:      ----
	// b: ----
	if (a[0] >= b[1]) return false;

	return true;
}

module.exports = rangesOverlap;
