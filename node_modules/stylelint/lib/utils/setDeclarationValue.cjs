// NOTICE: This file is generated by Rollup. To modify it,
// please instead edit the ESM counterpart and rebuild with Rollup (npm run build).
'use strict';

/** @typedef {import('postcss').Declaration} Declaration */

/**
 * @param {Declaration} decl
 * @param {string} value
 * @returns {Declaration} The declaration that was passed in.
 */
function setDeclarationValue(decl, value) {
	const raws = decl.raws;

	if (raws.value) {
		raws.value.raw = value;
	} else {
		decl.value = value;
	}

	return decl;
}

module.exports = setDeclarationValue;
