// NOTICE: This file is generated by Rollup. To modify it,
// please instead edit the ESM counterpart and rebuild with Rollup (npm run build).
'use strict';

/** @typedef {import('postcss').AtRule} AtRule */

/**
 * @param {AtRule} atRule
 * @param {string} params
 * @returns {AtRule} The atRulearation that was passed in.
 */
function setAtRuleParams(atRule, params) {
	const raws = atRule.raws;

	if (raws.params) {
		raws.params.raw = params;
	} else {
		atRule.params = params;
	}

	return atRule;
}

module.exports = setAtRuleParams;
