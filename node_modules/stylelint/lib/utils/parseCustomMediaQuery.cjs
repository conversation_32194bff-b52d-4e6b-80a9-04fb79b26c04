// NOTICE: This file is generated by Rollup. To modify it,
// please instead edit the ESM counterpart and rebuild with Rollup (npm run build).
'use strict';

const mediaQueryListParser = require('@csstools/media-query-list-parser');
const getAtRuleParams = require('./getAtRuleParams.cjs');

/**
 * @param {import('postcss').AtRule} atRule
 * @returns {ReturnType<typeof import('@csstools/media-query-list-parser').parseCustomMedia>}
 */
function parseCustomMediaQuery(atRule) {
	const mediaQuery = mediaQueryListParser.parseCustomMedia(getAtRuleParams(atRule), {
		preserveInvalidMediaQueries: true,
	});

	return mediaQuery;
}

module.exports = parseCustomMediaQuery;
