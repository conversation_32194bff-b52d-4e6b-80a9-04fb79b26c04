// NOTICE: This file is generated by Rollup. To modify it,
// please instead edit the ESM counterpart and rebuild with Rollup (npm run build).
'use strict';

const atKeywords = require('../reference/atKeywords.cjs');

const atRuleRegexes = {
	mediaName: /^media$/i,
	keyframesName: /^(-(o|moz|ms|webkit)-)?keyframes$/i,
	propertyName: /^property$/i,
	importName: /^import$/i,
	unsupportedNestingNames: new RegExp(
		`^((?!${[...atKeywords.nestingSupportedAtKeywords.values()].join('|')}).)*$`,
		'i',
	),
	layerName: /^layer$/i,
	containerName: /^container$/i,
	scopeName: /^scope$/i,
};

const descriptorRegexes = {
	syntaxName: /^syntax$/i,
};

const functionRegexes = {
	layer: /layer\((.*?)\)/i,
};

const propertyRegexes = {
	containerNameAndShorthandName: /^container(-name)?$/i,
};

exports.atRuleRegexes = atRuleRegexes;
exports.descriptorRegexes = descriptorRegexes;
exports.functionRegexes = functionRegexes;
exports.propertyRegexes = propertyRegexes;
