{"name": "<PERSON><PERSON>", "version": "1.0.0", "description": "theme for varvik site", "main": "functions.php", "scripts": {"dev": "npm-run-all --parallel start:js css:watch", "start:js": "wp-scripts start --webpack-src-dir=assets/src/js --output-path=assets/public/js", "build": "npm-run-all build:js css", "build:js": "wp-scripts build --webpack-src-dir=assets/src/js --output-path=assets/public/js", "css": "tailwindcss -i ./assets/src/css/input.css -o ./assets/public/css/main.css --minify", "css:watch": "tailwindcss -i ./assets/src/css/input.css -o ./assets/public/css/main.css --watch --minify", "lint:js": "wp-scripts lint-js assets/src/js", "editor-styles": "tailwindcss -i ./assets/src/css/editor/editor.css -o ./assets/public/css/editor.css --minify", "editor-styles:watch": "tailwindcss -i ./assets/src/css/editor/editor.css -o ./assets/public/css/editor.css --watch --minify"}, "repository": {"type": "git", "url": "<PERSON><PERSON>"}, "author": "visionmate", "license": "ISC", "devDependencies": {"@tailwindcss/cli": "^4.1.13", "@wordpress/scripts": "^30.24.0", "npm-run-all": "^4.1.5", "tailwindcss": "^4.1.13"}}